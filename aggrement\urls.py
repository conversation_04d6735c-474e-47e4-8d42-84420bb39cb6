"""
Urls
"""
from django.conf.urls import url
from django.urls import path
from . import views, views_admin

app_name = 'aggrement'

urlpatterns = [
    url(r'^$', views.aggrement_list_view, name='aggrementlist'),

    url(r'^aggrement_details/$', views.category_aggrement_details_view, name='category_aggrement_details'),

    url(r'^view/document/(?P<agreement_slug>[\w-]+)/$',
        views.agreement_details_view, name='agreement_details'),

    url(r'^search/aggrement/$',
        views.search_aggrement, name='aggrement_search_ajax'),

    url(r'^search/aggrement/ajax/$',
        views.search_aggrement_ajax_index, name='search_aggrement_ajax_index'),
     
    url(r'^search/aggrement/mobile/ajax/$',
        views.search_mobile_aggrement_ajax_index, name='search_mobile_aggrement_ajax_index'),

    url(r'^create/document/(?P<aggrement_slug>[\w-]+)/$',
        views.UpdateUserAggrement.as_view(), name='create_document'),

    url(r'^(?P<aggrement_pk>\d+)/$', views.add_user_aggrement, name='add_user_aggrement'),

    url(r'^user-document/list/$', views.user_agreements_list, name='user_agreements_list'),

    url(r'^pdf/view/(?P<aggrement_slug>[\w-]+)/$',
        views.user_agreement_pdf, name='user_agreement_pdf'),

    url(r'^word/view/(?P<aggrement_slug>[\w-]+)/$',
        views.user_agreement_in_word, name='user_agreement_in_word'),

    url(r'^share/view/(?P<aggrement_slug>[\w-]+)/$',
        views.agreement_share, name='agreement_share'),

    # url(r'^main/pdf/view/(?P<aggrement_slug>[\w-]+)/$',
    #     views.default_agreement_pdf_old, name='main_agreement_pdf_old'),  # not in use

    url(r'^main/pdf/view/$',
            views.default_agreement_pdf, name='main_agreement_pdf'),

    # url(r'^main/word/view/(?P<aggrement_slug>[\w-]+)/$',
    #     views.default_agreement_in_word_old, name='main_agreement_in_word_old'),  # not in use

    url(r'^main/word/view/$',
        views.default_agreement_in_word, name='main_agreement_in_word'),

    url(r'^main/share/view/(?P<aggrement_slug>[\w-]+)/$',
        views.default_agreement_share, name='main_agreement_share'),

    # url(r'^upload/document/$',
    #     views.ScannedDocumentCreateView.as_view(), name='upload_document'),

    # url(r'^update/scanned/document/(?P<scan_aggrement_slug>[\w-]+)/$',
    #     views.UpdateScannedAggrement.as_view(), name='update_scanned_docs'),


    ############################# Agreement Admin Views ##############################

    url(r'^all/agreement/admin/$',
        views_admin.AggrementListViewAdmin.as_view(), name='all_agreement_admin'),

    url(r'^all/user/agreement/admin/$',
        views_admin.UserAgrementListView.as_view(), name='all_user_agreement_admin'),
    
    # ================= jasmeet added =================

    # url(r'^success-checkout/$', views.success_checkout, name='success_checkout'),
    # url(r'^checkout/(?P<agreement_slug>[\w\-]+)/(?P<agreement_service_option>[\w\-]+)/$', views.checkout, name='checkout'), # changed to below 2 lines
    path('checkout/<str:agreement_slug>/<str:agreement_service_option>/', views.checkout, name='checkout'),
    # path('checkout/<str:agreement_slug>/<str:agreement_service_option>/<str:stamp_duty_state_name>/', views.checkout, name='checkout_with_stamp_duty_state'),
    
    url(r'^payment-initiate/$', views.payment_initiate, name='payment_initiate'),
    # url(r'^payment-initiate/(?P<order_id>\d+)/$', views.payment_initiate, name='payment_initiate'),
    # path('payment-initiate/<int:order_id>/', views.payment_initiate, name='payment_initiate'),
    # url(r'^check-status/(?P<order_id>\d+)/$', views.check_status, name='check_status'), # worked
    # url(r'^check-status/(?P<order_id>[a-zA-Z0-9]+)/$', views.check_status, name='check_status'),
    # path('check-status/<str:order_id>/<str:logged_in_user_id>/', views.check_status, name='check_status'),  # replaced with below
    
    
    path('check-status/<str:logged_in_user_id>/<str:order_id>/', views.check_status_order, name='check_status_order'),
    path('check-status/<str:logged_in_user_id>/<str:order_id>/<str:attempt_number>/', views.check_status_order_attempt_complete_payment, name='check_status_order_attempt_complete_payment'),
    
    
    # url(r'^check-status/(?P<order_id>[a-zA-Z0-9]+)/(?P<logged_in_user_id>[a-zA-Z0-9]+)/$', views.check_status, name='check_status'),
    # url(r'^check-status/(?P<order_id>[-\w]+)/(?P<logged_in_user_id>[-\w]+)/$', views.check_status, name="check_status"),
    url(r'^payment-done/(?P<order_id>\d+)/$', views.payment_done, name='payment_done'),
    url(r'^goto-login/$', views.goto_login, name='goto_login'),
    url(r'^goto-signup/$', views.goto_signup, name='goto_signup'),
    url(r'^check-user-status/$', views.check_user_status, name='check_user_status'),
    url(r'^process-checkout/$', views.process_checkout, name='process_checkout'),
    # url(r'^success-checkout/$', views.success_checkout, name='success_checkout'),
    url(r'^checkout-terms-conditions/$', views.checkout_terms_conditions, name='checkout_terms_conditions'),
    url(r'^category-agreements/$', views.get_category_agreements, name='get_category_agreements'),
    url(r'^agreement-detail/$', views.get_agreement_detail, name='get_agreement_detail'),
    
    url(r'^make-with-ai/(?P<agreement_slug>[\w\-]+)/$', views.make_with_ai, name='make_with_ai'),
    url(r'^chatbot/$', views.chatbot, name='chatbot'),
    url(r'^save-chat/$', views.save_chat, name='save_chat'),
    
    path('stamp-duty/', views.agreement_stamp_duty, name="stamp_duty"),
    # url(r'^checkout-payment/(?P<agreement_slug>[\w\-]+)/(?P<agreement_service_option>[\w\-]+)/$', views.checkout_payment_failed, name='checkout_payment_failed'),
    path('complete-payment/<str:order_id>', views.complete_pending_payment, name="complete_pending_payment"),
    
    path('get-agreement-with-fixed-stamp-duty/', views.get_agreement_with_fixed_stamp_duty, name="get_agreement_with_fixed_stamp_duty"), # extra
    path('get-agreement-with-different-stamp-duty/', views.get_agreement_with_different_stamp_duty, name="get_agreement_with_different_stamp_duty"), # extra
    path('check-estamp/', views.check_e_stamp_paper_with_state, name='check_e_stamp_paper_with_state'),
    path('most-used/', views.get_most_used_legal_agreements, name='get_most_used_legal_agreements'),
    
    path('is-exist-for-looking-for-agreement/', views.is_email_or_phone_already_exist_for_UserLookingForAgreement, name='is_email_or_phone_already_exist_for_UserLookingForAgreement'),
    path('looking-for-agreement/', views.looking_for_agreement, name='looking_for_agreement'),
    path('is-exist-for-user-agreement-order/', views.is_email_or_phone_already_exist_for_UserAgreementOrder, name='is_email_or_phone_already_exist_for_UserAgreementOrder'),
    path('order/<str:logged_in_user_id>/<str:order_id>/', views.get_completed_order_detail, name='get_completed_order_detail'),
    path('get-agreement-orders-for-sales-notifications/', views.get_agreement_orders_for_sales_notifications, name="get_agreement_orders_for_sales_notifications"),
    
    path('ask-a-question/<str:agreement_slug>/', views.faq_query_view, name="faq_query"),
    # ============================================================
    
    # path('add-fixed/', views.add_fixed), # extra
    # path('add-different/', views.add_different), # extra
    # path('set-fixed/', views.agreement_set_to_fixed), # extra
    # path('remove-objects/', views.remove_objects), # extra
    # path('add-objects/', views.add_objects), # extra
    # path('my-view/', views.my_view, name='my-view'),
    # path('my-view/<str:state_name>/', views.my_view, name='my-view-state'), # extra
    
    # path('check-textbody/', views.check_textbody), # extra
]

