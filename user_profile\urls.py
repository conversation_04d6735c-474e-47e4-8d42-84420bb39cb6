"""
URLs
"""
from django.conf.urls import url
from django.urls import path
from . import views, views_admin

app_name = 'user_profile'

urlpatterns = [

	url(r'^$', views.ProfileDetailsView.as_view(),
        name='profile_details'),

        url(r'^update/lawyer-data/$', views.LawyerProfileUpdateView.as_view(),
        name='update_lawyer_details'),

	url(r'^update/$', views.ProfileUpdateView.as_view(),
        name='profile_update'),

	url(r'^update-to-lawyer/$', views.UpgradeToLawyerCreateView.as_view(),
        name='update_lawyer'),

	url(r'^update-lawyer-data/$', views.UpdateLawyerData.as_view(),
        name='update_lawyer_data'),

	url(r'^complete-your-kyc/$', views.KYCCreateView.as_view(),
        name='kyc'),

	url(r'^update-your-kyc/$', views.KYCUpdateView.as_view(),
        name='kyc_update'),

        url(r'^client_reminder_system/$', views.client_reminder_system,
        name='client_reminder_system'),

        url(r'^lawyer-category/(?P<category_slug>[\w-]+)/$', views.lawyer_category_details,
        name='lawyer_category_details'),

        url(r'^lawyer-sub-category/(?P<sub_category_slug>[\w-]+)/$', views.lawyer_sub_category_details,
        name='lawyer_sub_category_details'),

        url(r'^final-application/$', views.final_lawyer_submit_views,
        name='final_lawyer_submit_views'),

        url(r'^contracts-list/$', views.ContractListView.as_view(),
        name='contracts_list'),

        url(r'^drafts-list/$', views.DraftsListView.as_view(),
        name='drafts_list'),



        url(r'^drafts-list/delete/(?P<drafts_pk>\d+)/$', views.delete_drafts, name='drafts_delete'),

        url(r'^contracts-list/delete/(?P<contracts_pk>\d+)/$', views.delete_contracts, name='delete_contracts'),



        url(r'^search/lawyers/$', views.search_lawyers,
        name='search_lawyers'),


        #################### Admin Views URLS #################################

        url(r'^customer/list/admin/$', views_admin.CustomerListViewAdmin.as_view(),
        name='admin_customer_list'),

        url(r'^lawyer/list/admin/$', views_admin.LawyerListViewAdmin.as_view(),
        name='admin_lawyer_list'),

        url(r'^deactive/user/list/admin/$', views_admin.DeactivatedUserListViewAdmin.as_view(),
        name='admin_deactive_user_list'),

        url(r'^deactivate/user/(?P<profile_pk>\d+)/$', views_admin.delete_user,
        name='delete_user'),

        url(r'^activate/user/(?P<profile_pk>\d+)/$', views_admin.activate_user,
        name='activate_user'),

        url(r'^pending/kycs/$', views_admin.PendingKycsViewAdmin.as_view(),
        name='admin_pending_kycs_info'),

        url(r'^pending/lawyer/info/$', views_admin.PendingLawyerDetails.as_view(),
        name='admin_pending_lawyer_info'),

        url(r'^accepted/kycs/$', views_admin.AcceptedKycsViewAdmin.as_view(),
        name='admin_accepted_kycs_info'),

        url(r'^accepted/lawyer/info/$', views_admin.AcceptedLawyerDetails.as_view(),
        name='admin_accepted_lawyer_info'),

        url(r'^rejected/kycs/$', views_admin.RejectedKycsViewAdmin.as_view(),
        name='admin_rejected_kycs_info'),

        url(r'^rejected/lawyer/info/$', views_admin.RejectedLawyerDetails.as_view(), name='admin_rejected_lawyer_info'),

        url(r'^kyc/(?P<kyc_pk>\d+)/details/admin/$', views_admin.kyc_details_view, name='kyc_details_view'),

        url(r'^accept/kyc/(?P<kyc_pk>\d+)/$', views_admin.accept_kyc_view, name='accept_kyc_view'),

        url(r'^reject/kyc/(?P<kyc_pk>\d+)/$', views_admin.kyc_rejection_form, name='kyc_rejection_form'),

        url(r'^lawyer/(?P<lawyer_pk>\d+)/details/admin/$', views_admin.lawyer_details_view, name='lawyer_details_view'),


        url(r'^accept/lawyer/(?P<lawyer_pk>\d+)/$', views_admin.accept_lawyer_view, name='accept_lawyer_view'),

        url(r'^reject/lawyer/(?P<lawyer_pk>\d+)/$', views_admin.lawyer_rejection_form, name='lawyer_rejection_form'),
        
]