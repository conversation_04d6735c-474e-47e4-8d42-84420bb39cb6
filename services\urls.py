"""
Urls
"""
from django.conf.urls import url
from . import views
from django.urls import path

app_name = 'services'

urlpatterns = [
    path('ask-a-question/<str:service_slug>/', views.faq_query_view, name="faq_query"),

    path('get-service-orders-for-sales-notifications/', views.get_service_orders_for_sales_notifications, name="get_service_orders_for_sales_notifications"),

    # ================ ORDER ===================================
    url(r'^goto-login-get-a-quote/$', views.goto_login_get_a_quote, name='goto_login_get_a_quote'),
    url(r'^goto-signup-get-a-quote/$', views.goto_signup_get_a_quote, name='goto_signup_get_a_quote'),
    
    url(r'^check-user-status/$', views.check_user_status, name='check_user_status'),
    url(r'^goto-login/$', views.goto_login, name='goto_login'),
    url(r'^goto-signup/$', views.goto_signup, name='goto_signup'),
    url(r'^checkout/(?P<service_slug>[\w\-]+)/$', views.checkout, name='checkout'),
    
    url(r'^process-checkout-quote/$', views.process_checkout_quote, name='process_checkout_quote'),
    
    url(r'^process-checkout/$', views.process_checkout, name='process_checkout'),
    path('payment/', views.payment_handler, name='payment_handler'),
    path('payment-completed/<str:order_id>/', views.payment_completed, name='payment_completed'),
    path('payment-failed/', views.payment_failed, name='payment_failed'),
    
    
    path('checkout-terms-conditions/', views.checkout_terms_conditions, name='service_checkout_terms_conditions'),
    path('is-exist-for-user-service-order/', views.is_email_or_phone_already_exist_for_UserServiceOrder, name='is_email_or_phone_already_exist_for_UserServiceOrder'),
    path('is-exist-for-user-get-quote-service-order/', views.is_email_or_phone_already_exist_for_UserGetQuoteService, name='is_email_or_phone_already_exist_for_UserGetQuoteService'),
    
    # url(r'^services/category/(?P<category_slug>[\w-]+)/$',
    #     views.service_category_list, name='services_category_list'),
    
    # above changed to below    
    url(r'^(?P<category_slug>[\w-]+)/$',
        views.service_category_list, name='services_category_list'),

    
    path('checkout-get-a-quote/<str:service_slug>/', views.checkout_get_a_quote, name='checkout_get_a_quote'),
    path('get-a-quote-service-completed/<str:get_quote_service_id>/', views.get_a_quote_service_completed, name='get_a_quote_service_completed'),
    path('<str:category_slug>/<str:service_slug>/', views.service_detail, name='services_details2'),
    path('', views.services_list, name='services_list'),
    
    # above changed to below
    # url(r'^sub-category/(?P<sub_category_slug>[\w-]+)/$',
    #     views.services_sub_category_details_view, name='services_sub_category'),
    
    # above changed to below
    # path('<str:category_slug>/<str:sub_category_slug>/', views.services_sub_category_details_view, name='services_sub_category'),
    
    path('complete-payment/<str:order_id>', views.complete_pending_payment, name="complete_pending_payment"),
    
    
    
]
