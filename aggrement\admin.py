"""
Admin
"""
from django import forms
from django.contrib import admin
from django.contrib.admin import widgets
from django.urls import reverse
from django_summernote.admin import SummernoteModelAdmin

from aggrement.views import send_email_for_completed_order_status_for_agreement_order
from .forms import FAQAdminForm
from .models import AggrementCatagories, Aggrement, AgreementWithDifferentStampDutyInAllStates, AgreementWithFixedStampDutyInAllStates, AgreementStampDutyState, EStampPaperState, LookingForAgreement, OrderAgreementRetryAttempt, UserAggrements, UserLookingForAgreement, UserOrder, OrderAgreement, StampDutyChargesWithImages, FAQ
from .models import UserMakeAgreementWithAI, UserBotMessage, Chat, DownloadAIResponse, UserDownloadAIWord, UserDownloadAIPdf
from django.utils.html import format_html
import threading


class ManyToManyAdmin(admin.ModelAdmin):
    def formfield_for_manytomany(self, db_field, request=None, **kwargs):
        kwargs['widget'] = widgets.FilteredSelectMultiple(
            db_field.verbose_name,
            db_field.name in self.filter_vertical
        )

        return super(admin.ModelAdmin, self).formfield_for_manytomany(
            db_field, request=request, **kwargs)



class StampDutyChargesWithImagesInline(admin.TabularInline):
    model = StampDutyChargesWithImages
    extra = 1  
    fields = ['amount', 'image']
     


class AgreementWithFixedStampDutyInAllStatesInline(admin.TabularInline):
    model = AgreementWithFixedStampDutyInAllStates
    extra = 1    
    inlines = [StampDutyChargesWithImagesInline]


class AgreementWithDifferentStampDutyInAllStatesInline(admin.TabularInline):
    model = AgreementWithDifferentStampDutyInAllStates
    extra = 1 
    inlines = [StampDutyChargesWithImagesInline]


class AggrementAdminForm(forms.ModelForm):
    class Meta:
        model = Aggrement
        fields = '__all__'

        
class AggrementAdmin(SummernoteModelAdmin):
    """
    Addrement Admin
    """
    model = Aggrement
    list_display = ['id', 'title', 'date', 'act', 'section',]
    search_fields = ['title', 'act']
    summernote_fields  = ('textbody','guideline',)
    inlines = [AgreementWithFixedStampDutyInAllStatesInline, AgreementWithDifferentStampDutyInAllStatesInline]


class StampDutyChargesWithImagesAdmin(admin.ModelAdmin):
    model = StampDutyChargesWithImages
    search_fields = ['amount',]
    list_display = ['id', 'amount', 'display_image']
    
    def display_image(self, obj):
        if obj.image:
            return format_html('<img src="{}" style="max-height: 100px; max-width: 100px;" />', obj.image.url)
    
    display_image.short_description = 'Image'
    

class AgreementWithFixedStampDutyInAllStatesAdmin(admin.ModelAdmin):
    model = AgreementWithFixedStampDutyInAllStates
    search_fields = ['agreement__title',]
    list_display = ['id', 'agreement', 'display_amount', 'display_image']
    
    def display_amount(self, obj):
        return obj.stamp_duty.amount
    display_amount.short_description = 'Stamp Duty Amount'

    def display_image(self, obj):
        return format_html('<img src="{}" style="max-height: 100px; max-width: 100px;" />', obj.stamp_duty.image.url)
    display_image.short_description = 'Stamp Duty Image'
    

class AgreementStampDutyStateAdmin(admin.ModelAdmin):
    model = AgreementStampDutyState
    search_fields = ['state_name']
    list_display = ['id', 'state_name']

admin.site.register(AgreementStampDutyState, AgreementStampDutyStateAdmin)


class EStampPaperStateAdmin(admin.ModelAdmin):
    model = EStampPaperState
    search_fields = ['state_name']
    list_display = ['id', 'state_name', 'is_e_stamp_paper_allowed']

admin.site.register(EStampPaperState, EStampPaperStateAdmin)



class AgreementWithDifferentStampDutyInAllStatesAdmin(admin.ModelAdmin):
    model = AgreementWithDifferentStampDutyInAllStates
    search_fields = ['agreement__title',]
    list_display = ['id', 'agreement', 'display_state', 'display_amount', 'display_image']
    
    def display_state(self, obj):
        if obj.state:
            return obj.state.state_name
    display_state.short_description = 'State'
    
    def display_amount(self, obj):
        return obj.stamp_duty.amount
    display_amount.short_description = 'Stamp Duty Amount'

    def display_image(self, obj):
        return format_html('<img src="{}" style="max-height: 100px; max-width: 100px;" />', obj.stamp_duty.image.url)
    display_image.short_description = 'Stamp Duty Image'
    

admin.site.register(StampDutyChargesWithImages, StampDutyChargesWithImagesAdmin)
admin.site.register(AgreementWithFixedStampDutyInAllStates, AgreementWithFixedStampDutyInAllStatesAdmin)
admin.site.register(AgreementWithDifferentStampDutyInAllStates, AgreementWithDifferentStampDutyInAllStatesAdmin)

class UserAgreementAdmin(admin.ModelAdmin):
	"""
	User Agreement Admin
	"""
	model = UserAggrements
	list_display = ['user', 'aggrement', 'date']



admin.site.register(AggrementCatagories)
admin.site.register(Aggrement, AggrementAdmin)
admin.site.register(UserAggrements, UserAgreementAdmin)
# admin.site.register(ScannedUserAgreements)  # not in use


class AttemptInline(admin.TabularInline):
    model = OrderAgreementRetryAttempt
    fields = ['id', 'attempt_number', 'payment_status', 'created_at']  # Add any other fields you want to display
    readonly_fields = ['id', 'attempt_number', 'payment_status', 'created_at']   # Make the fields read-only
    extra = 0
    # show_change_link = True  # Display a link to the attempt's detail page
    ordering = ('-id',)  # Display attempts in reverse order based on the 'id' field
    can_delete = False  # Remove the delete checkbox

class OrderAgreementAdmin(admin.ModelAdmin):
    """
    OrderAgreementAdmin Admin
    """
    model = OrderAgreement
    list_display = ['id', 'user_id', 'agreement_title', 'agreement_service_option' ,'amount', 'payment_mode', 'payment_status', 'order_status', 'created_at', 'to_complete_payment_url', 'payment_completed_url', 'updated_at',]
    readonly_fields = ('created_at', 'updated_at',) 
    inlines = [AttemptInline,]
    search_fields = ['id', 'agreement__title', 'agreement_service_option', 'payment_mode', 'payment_status', 'amount', 'user__phone_number', 'user__country__country_name', 'user__state__state_name', 'user__email', 'user__first_name', 'user__last_name', 'user__user__username']
    
    
    def agreement_title(self, obj):
        # Define a custom method to get the agreement title
        if obj.agreement:
            return obj.agreement.title  # Replace 'title' with the actual field name of the agreement title
        return "N/A"  # Handle the case where 'agreement' is None

    agreement_title.short_description = 'Agreement Title'  # Set a custom column header in the admin list view
    
    def user_id(self, obj):
        # Define a custom method to get the agreement title
        if obj.user:
        #     return obj.user.id  # Replace 'title' with the actual field name of the agreement title
            s = str(obj.user.id)
            if obj.user.email:
                s += " | " + "Email: " + str(obj.user.email)
            if obj.user.first_name:
                s += " | " + "First name: " + str(obj.user.first_name)
            if obj.user.last_name:
                s += " | " + "Last name: " + str(obj.user.last_name)
            if obj.user.phone_number:
                s += " | " + "Phone: " +  str(obj.user.phone_number)
            if obj.user.state:
                s += " | " + "State: " + str(obj.user.state)
            if obj.user.city:
                s += " | " + "City: " + str(obj.user.city)
            if obj.user.user:
                s += " | " + "User: " + str(obj.user.user)
            return s   
        return "N/A"  # Handle the case where 'agreement' is None
    user_id.short_description = 'User Id' 
    
    
    def save_model(self, request, obj, form, change):
        if obj.agreement_service_option in ['make_with_ai', 'make_with_legal_help']:
            if obj.order_status == 'COMPLETED':
                ag_obj = Aggrement.objects.filter(id=obj.agreement.id).first()
                if obj.agreement_service_option == 'make_with_ai':
                    ag_obj.make_with_ai_count += 1
                if obj.agreement_service_option == 'make_with_legal_help':
                    ag_obj.make_with_legal_help_count += 1
                ag_obj.save()

                agreement_slug = ag_obj.slug
                agreement_service_option = obj.agreement_service_option

                url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])
                if obj.user.email:
                    user_email = obj.user.email
                else:
                    user_email = obj.user.user.email
            
                thread = threading.Thread(target=send_email_for_completed_order_status_for_agreement_order, args=(obj, user_email, url_PlaceAgreementOrder))
                thread.start()
        super().save_model(request, obj, form, change)
    
admin.site.register(OrderAgreement, OrderAgreementAdmin)

class OrderAgreementRetryAttemptAdmin(admin.ModelAdmin):
    """
    OrderAgreementRetryAttempt Admin
    """
    model = OrderAgreementRetryAttempt
    list_display = ['id', 'order', 'attempt_number', 'payment_status', 'created_at']
    readonly_fields = ('created_at',) 
    search_fields = ['id', 'order__id', 'attempt_number', 'payment_status',]


admin.site.register(OrderAgreementRetryAttempt, OrderAgreementRetryAttemptAdmin)


class UserOrderAdmin(admin.ModelAdmin):
    """
    UserOrder Admin
    """
    model = UserOrder
    list_display = ['id', 'first_name', 'last_name', 'email', 'phone_number', 'state', 'city', 'user',]
    search_fields = ['first_name', 'last_name', 'email', 'phone_number', 'user__email', 'state__state_name', 'country__country_name']
    
admin.site.register(UserOrder, UserOrderAdmin)


class UserBotMessageAdmin(admin.ModelAdmin):
    model = UserBotMessage
    list_display = ['id', 'sender', 'content' ,'timestamp']

admin.site.register(UserBotMessage, UserBotMessageAdmin)


class DownloadAIResponseAdmin(SummernoteModelAdmin):
    """
    DownloadAIResponse Admin
    """
    model = DownloadAIResponse
    list_display = ['id', 'timestamp',]
    summernote_fields  = ('content',)

admin.site.register(DownloadAIResponse, DownloadAIResponseAdmin)
admin.site.register([UserMakeAgreementWithAI, UserDownloadAIWord, UserDownloadAIPdf])

class ChatAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'agreement', 'timestamp')

admin.site.register(Chat, ChatAdmin)



class UserLookingForAgreementAdmin(admin.ModelAdmin):
    model = UserLookingForAgreement
    list_display = [field.name for field in model._meta.fields]
    search_fields = [field.name for field in model._meta.fields]

admin.site.register(UserLookingForAgreement, UserLookingForAgreementAdmin)


class LookingForAgreementAdmin(admin.ModelAdmin):
    model = LookingForAgreement
    list_display = [field.name for field in model._meta.fields]
    search_fields = ['user__email', 'user__name', 'user__phone_number', 'agreement_title']

admin.site.register(LookingForAgreement, LookingForAgreementAdmin)



class FAQAdmin(admin.ModelAdmin):
    form = FAQAdminForm

admin.site.register(FAQ, FAQAdmin)
