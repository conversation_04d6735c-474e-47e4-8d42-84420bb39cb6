from django.contrib import admin

from email_configuration.views import send_kyc_verified_mail, send_kyc_rejection_mail

from .models import (
    Profile, CategoryPractice, UpgradeToLawyer, KYC, Specialization, Courts,
    PracticeArea, Language, LawyerCategory, LawyerSubCategory, WorkingExperience,
    FireBaseCredentials
)
# Register your models here.

class KYCAdmin(admin.ModelAdmin):
	"""
	KYC Admin Model
	"""
	model = KYC
	list_display = ['first_name', 'father_name', 'state', 'is_verified','is_rejected']
	search_fields = ['first_name','last_name']

	def save_model(self, request, obj, form, change):
		if obj.is_verified == True:
			send_kyc_verified_mail(kyc=obj)
		if obj.is_rejected == True:
			send_kyc_rejection_mail(kyc=obj)
		super(KYCAdmin, self).save_model(request, obj, form, change)


class ProfileAdmin(admin.ModelAdmin):
    """
    Profile Admin
    """
    model = Profile
    list_display = ['user','user_type', 'email', 'phone_no', 'country', 'state', 'city']
    search_fields = ('email', 'phone_no',)




admin.site.register(WorkingExperience)
# admin.site.register(FireBaseCredentials)
admin.site.register(LawyerSubCategory)
admin.site.register(LawyerCategory)
admin.site.register(CategoryPractice)
admin.site.register(UpgradeToLawyer)
admin.site.register(Profile,ProfileAdmin)
admin.site.register(KYC, KYCAdmin)
admin.site.register(Specialization)
admin.site.register(Courts)
admin.site.register(PracticeArea)
admin.site.register(Language)
