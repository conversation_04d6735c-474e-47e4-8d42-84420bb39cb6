from datetime import datetime
from django.utils import timezone
import hashlib
import re
from docx import Document
from html.parser import HTMLParser

from django.db.models import Q
from django.utils.html import strip_tags
from django.shortcuts import render, redirect
from django.urls import reverse
from django.http import JsonResponse, HttpResponse, HttpResponseRedirect
from django.contrib import messages
from django.db.models import Count, Value, Case, When, IntegerField
from django.db.models.functions import Coalesce
from django.contrib.auth.decorators import login_required
from django.views.generic import UpdateView, CreateView
from django.template.loader import render_to_string
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import requests

from app.common import CommonMixin
from contract_seo.models import CommonSeo
from user_profile.models import Profile

from .forms import AgreementFAQForm, UserAggrementsForm, ScannedUserAgreementsForm
from .utils import render_to_pdf
from .models import FAQ, PAYMENT_STATUS_CHOICES, Aggrement, AggrementCatagories, AgreementWithDifferentStampDutyInAllStates, AgreementWithFixedStampDutyInAllStates, DownloadAIResponse, EStampPaperState, LookingForAgreement, OrderAgreement, OrderAgreementRetryAttempt, StampDutyChargesWithImages, UserAggrements, ScannedUserAgreements, UserDownloadAIPdf, UserDownloadAIWord, UserLookingForAgreement, UserOrder
from .models import UserMakeAgreementWithAI, UserBotMessage, Chat
from django.views.decorators.csrf import csrf_exempt
import re
from sample.settings import OPEN_AI_KEY
from django.shortcuts import render
import openai
import json
import base64
from .phonepe_api import make_pay_page_request, make_status_request
from app.models import CountryMaster, StateMaster
from django.http import JsonResponse
from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from io import BytesIO
from django.contrib.auth import get_user_model
from django.contrib.auth import logout
from email_configuration.views import send_agreement_order_email, send_faq_inquiry_email
from sample.settings import DEFAULT_FROM_EMAIL as ADMIN_EMAIL
import threading
import urllib
from django.conf import settings
from django.db.models import Sum
import datetime as dt
import time
from django.db.models import Value, CharField
from django.db.models.functions import Coalesce


# Create your views here.


# using in admin.py
def send_email_for_completed_order_status_for_agreement_order(order_obj, user_email, url_PlaceAgreementOrder):
	# send email to customer about order completion
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-COMPLETED-MAIL')
	# # send email to admin about order completion
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-COMPLETED-MAIL', url_PlaceAgreementOrder)


def send_email_for_completed_word_or_pdf_agreement_order_with_successful_payment_completed_order(order_obj, user_email, url_PlaceAgreementOrder):
	# send email to customer about placed and payment successful
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-PLACED-AND-PAYMENT-SUCCESS-MAIL')
	# send email to admin about placed and payment successful
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-PLACED-AND-PAYMENT-SUCCESS-MAIL', url_PlaceAgreementOrder)

	# send email to customer about order completion
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-COMPLETED-MAIL')
	# send email to admin about order completion
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-COMPLETED-MAIL', url_PlaceAgreementOrder)


def send_email_for_completed_reinitiated_word_or_pdf_agreement_order_with_successful_payment_completed_order(order_obj, user_email, url_PlaceAgreementOrder):
    # send email to customer about payment successful
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-SUCCESS-MAIL')
	# send email to admin about payment successful
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-SUCCESS-MAIL', url_PlaceAgreementOrder)

	# send email to customer about order completion
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-COMPLETED-MAIL')
	# send email to admin about order completion
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-COMPLETED-MAIL', url_PlaceAgreementOrder)


def send_email_for_agreement_order_with_successful_payment_order(order_obj, user_email, url_PlaceAgreementOrder):
	# send email to customer about payment successful
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-PLACED-AND-PAYMENT-SUCCESS-MAIL')
	# send email to admin about payment successful
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-PLACED-AND-PAYMENT-SUCCESS-MAIL', url_PlaceAgreementOrder)



def send_email_for_reinitiated_agreement_order_with_successful_payment_order(order_obj, user_email, url_PlaceAgreementOrder):
    # send email to customer about payment successful
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-SUCCESS-MAIL')
	# send email to admin about payment successful
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-SUCCESS-MAIL', url_PlaceAgreementOrder)


def delayed_check_and_send_email_to_customer_for_placed_and_pending_agreement_order(order_id, user_email):
	time.sleep(180)  # Wait for 180 seconds (3 minutes)
	# Get the order object again
	orderAgreement_obj = OrderAgreement.objects.filter(id=order_id).first()
	# Check the payment status and send an email if not completed
	if orderAgreement_obj.payment_status != "PAYMENT_SUCCESS":
		send_agreement_order_email(orderAgreement_obj, user_email, 'AGREEMENT-ORDER-PLACED-AND-PAYMENT-PENDING-MAIL')


def delayed_check_and_send_email_to_customer_for_reinitiated_and_pending_agreement_order(order_id, user_email):
	time.sleep(180)  # Wait for 420 seconds (7 minutes)
	# Get the order object again
	orderAgreement_obj = OrderAgreement.objects.filter(id=order_id).first()
	# Check the payment status and send an email if not completed
	if orderAgreement_obj.payment_status != "PAYMENT_SUCCESS":
		send_agreement_order_email(orderAgreement_obj, user_email, 'AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-PENDING-MAIL')
	

def delayed_check_and_send_email_to_admin_for_placed_and_pending_agreement_order(order_id, url_PlaceAgreementOrder):
	time.sleep(420)  # Wait for 420 seconds (10 minutes)
	# Get the order object again
	orderAgreement_obj = OrderAgreement.objects.filter(id=order_id).first()
	# Check the payment status and send an email if not completed
	if orderAgreement_obj.payment_status != "PAYMENT_SUCCESS":
		send_agreement_order_email(orderAgreement_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-PLACED-AND-PAYMENT-PENDING-MAIL', url_PlaceAgreementOrder)


def delayed_check_and_send_email_to_admin_for_reinitiated_and_pending_agreement_order(order_id, url_PlaceAgreementOrder):
	time.sleep(420)  # Wait for 420 seconds (7 minutes)
	# Get the order object again
	orderAgreement_obj = OrderAgreement.objects.filter(id=order_id).first()
	# Check the payment status and send an email if not completed
	if orderAgreement_obj.payment_status != "PAYMENT_SUCCESS":
		send_agreement_order_email(orderAgreement_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-REINITIATED-AND-PAYMENT-PENDING-MAIL', url_PlaceAgreementOrder)


def send_email_for_payment_failed_agreement_order(order_obj, user_email, url_PlaceAgreementOrder):
	# send email to customer
	send_agreement_order_email(order_obj, user_email, 'AGREEMENT-ORDER-PAYMENT-FAILED-MAIL')
	# send email to admin
	send_agreement_order_email(order_obj, ADMIN_EMAIL, 'ADMIN-AGREEMENT-ORDER-PAYMENT-FAILED-MAIL', url_PlaceAgreementOrder)


def send_email_for_agreement_faq_inquiry(obj, user_email, source_url, faq_page_url):
	# send email to customer
	send_faq_inquiry_email(obj, user_email, 'AGREEMENT-FAQ-INQUIRY-MAIL', faq_page_url=faq_page_url)
	# send email to admin
	send_faq_inquiry_email(obj, ADMIN_EMAIL, 'AGREEMENT-ADMIN-FAQ-INQUIRY-MAIL', source_url=source_url)



def  category_aggrement_details_view(request):
	"""
	Category aggrement Details View 
	"""
	context={
		
	}
	return render(request,'category_aggrement_details.html',context)


def aggrement_list_view2(request):  # not in use
	"""
	Aggrement List View
	"""
	aggrements = Aggrement.objects.all().order_by('id')

	aggrement_category_list = AggrementCatagories.objects.all().order_by('id')

	page = request.GET.get('page', 1)
	paginator = Paginator(aggrements, 13)
	try:
		aggrement_list = paginator.page(page)
	except PageNotAnInteger:
		aggrement_list = paginator.page(1)
	except EmptyPage:
		aggrement_list = paginator.page(paginator.num_pages)

	seo = CommonSeo.objects.filter(sample__exact='LEGAL-DOCUMENTS-LIBRARY-PAGE-SEO').first()

	agreement_count = aggrements.aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created
	
	context = {
		'aggrement_list' : aggrement_list,
		'aggrement_category_list' : aggrement_category_list,
		'seo' : seo,
		'agreement_count' : agreement_count
	}
	return CommonMixin.render(request, 'aggrement_list.html', context)  # aggrement_list.html
	
	
def aggrement_list_view(request):
	"""
	Aggrement List View
	"""

	if request.method == 'POST':
		if 'category_id' in request.POST:
			# 'category_id' is present in request.POST
			category_id = request.POST.get('category_id')
			request.session['selected_category_id'] = category_id 
		else:
			category_id = request.session.get('selected_category_id') 
	else:
		category_id = 1
		
	aggrement_list = Aggrement.objects.filter(aggrement_category__id=category_id).order_by('id')
	aggrement = Aggrement.objects.filter(aggrement_category__id=category_id).first()
	
	aggrement_category_list = AggrementCatagories.objects.all().order_by('id')
	seo = CommonSeo.objects.filter(sample__exact='LEGAL-DOCUMENTS-LIBRARY-PAGE-SEO').first()
	agreement_count = aggrement_list.aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created
	countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
	context = {
		'aggrement_list' : aggrement_list,
		'aggrement_category_list' : aggrement_category_list,
		'seo' : seo,
		'agreement_count' : agreement_count,
		'agreement_details': aggrement,
		'category_id': category_id,
		'countries': countries,
		'states': states
	}
	return CommonMixin.render(request, 'aggrement_list2.html', context)  # aggrement_list.html


def agreement_stamp_duty(request):
	if request.method == 'POST':
		aggrement_slug = request.POST.get('agreement_slug')
		agreement_details = Aggrement.objects.filter(slug=aggrement_slug).first()
		if agreement_details:
			if agreement_details.is_stamp_duty_needed:
				if agreement_details.is_stamp_duty_needed and agreement_details.is_stamp_duty_fixed_for_all_states:
					fixed_object = AgreementWithFixedStampDutyInAllStates.objects.filter(agreement=agreement_details)
					if fixed_object:
						fixed_objects_values = fixed_object.values('stamp_duty__amount', 'stamp_duty__image')
						fixed_objects_values_list = list(fixed_objects_values)
						response_data = {
							'success': True,
							'stamp_duty' : "fixed",
							'data': fixed_objects_values_list,
						}
						return JsonResponse(response_data, safe=False)
				elif agreement_details.is_stamp_duty_needed and not agreement_details.is_stamp_duty_fixed_for_all_states:
					different_object = AgreementWithDifferentStampDutyInAllStates.objects.filter(agreement=agreement_details)
					if different_object:
						different_objects_values = different_object.values('state__state_name', 'stamp_duty__amount', 'stamp_duty__image')
						different_objects_values_list = list(different_objects_values)
						response_data = {
							'success': True,
							'stamp_duty' : "different",
							'data': different_objects_values_list,
						}
			# else:
			#     response_data = {
			#         'success': True,
			#         'stamp_duty': "none"
			#     }
			else:
				if agreement_details.is_court_fee_needed:
					court_fee = True
				else:
					court_fee = False
				response_data = {
					'success': True,
					'stamp_duty': "none",
					'court_fee': court_fee
				}
		else:
			response_data = {
				'success': True,
				'message': f'agreement not found.',
			}
		return JsonResponse(response_data)
	return JsonResponse({'error': 'Invalid request'})


def agreement_details_view(request, agreement_slug):
	# 	"""
	# 	Agreement Details View
	# 	"""
	agreement_details = Aggrement.objects.filter(slug=agreement_slug).first()
	countries = countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
	questions = FAQ.objects.filter(agreement=agreement_details, status="PUBLISHED").order_by('id')
	page = request.GET.get('page', 1)
	paginator = Paginator(questions, 12)
	
	try:
		questions_list = paginator.page(page)
	except PageNotAnInteger:
		questions_list = paginator.page(1)
	except EmptyPage:
		questions_list = paginator.page(paginator.num_pages)

	context = {
		'agreement_details' : agreement_details,
		'countries': countries,
		'states': states,
		'questions_list' : questions_list
	}
	return CommonMixin.render(request, 'aggrement_details2.html', context)  # aggrement_details.html


def agreement_details_view1(request, agreement_slug):  # jasmeeet added
	"""
	Agreement Details View
	"""
	agreement_details = Aggrement.objects.filter(slug=agreement_slug).first()

	context = {
		'agreement_details' : agreement_details
	}
	return CommonMixin.render(request, 'aggrement_details.html', context)



def search_aggrement(request):
	"""
	Ajax Request for searching of aggrement
	"""
	query = request.GET.get('search', None)

	if query:
		aggrements = Aggrement.objects.filter(
			Q(title__icontains=query) |
			Q(act__icontains=query) |
			Q(aggrement_category__name__icontains=query) |
			Q(description__icontains=query)
			).order_by("id")
	else:
		aggrements = Aggrement.objects.all().order_by('id')

	page = request.GET.get('page', 1)
	paginator = Paginator(aggrements, 13)
	try:
		aggrement_list = paginator.page(page)
	except PageNotAnInteger:
		aggrement_list = paginator.page(1)
	except EmptyPage:
		aggrement_list = paginator.page(paginator.num_pages)

	context = {
		'aggrement_list' : aggrement_list
	}

	if request.is_ajax():
		html = render_to_string('aggrement_ajax_list.html',
								context, request=request)

	data = {

		'html': html,
		'query': query,
	}
	return JsonResponse(data)

def search_aggrement_ajax_index(request):
	"""
	Ajax Request for searching of aggrement
	"""
	query = request.GET.get('search', None)

	if query:
		aggrements = Aggrement.objects.filter(
			Q(title__icontains=query) |
			Q(title__startswith=query) 
			# Q(aggrement_category__name__startswith=query) |
			# Q(aggrement_category__name__icontains=query)
			# Q(act__startswith=query) |
			# Q(act__icontains=query) |
			).order_by("id").distinct()[:10]
	else:
		aggrements = Aggrement.objects.all().order_by('id')

	context = {
		'aggrement_list' : aggrements
	}

	if request.is_ajax():
		html = render_to_string('aggrement_ajax_index_list.html',
								context, request=request)

	data = {

		'html': html,
		'query': query,
	}
	return JsonResponse(data)


def search_mobile_aggrement_ajax_index(request):
	"""
	Ajax Request for searching of aggrement
	"""
	query = request.GET.get('search', None)

	if query:
		aggrements = Aggrement.objects.filter(
			Q(title__icontains=query) |
			Q(act__icontains=query) |
			Q(aggrement_category__name__icontains=query)
			).order_by("id")[:10]
	else:
		aggrements = Aggrement.objects.none()

	context = {
		'aggrement_list' : aggrements
	}

	if request.is_ajax():
		html = render_to_string('aggrement_ajax_mobile_list.html',
								context, request=request)

	data = {

		'html': html,
		'query': query,
	}
	return JsonResponse(data)

	

class UpdateUserAggrement(LoginRequiredMixin, CommonMixin, UpdateView):
	"""
	User Aggrement Update View
	"""
	model = UserAggrements
	form_class = UserAggrementsForm
	template_name = "aggrement_form.html"

	def get_object(self):
		user_agreement = UserAggrements.objects.filter(slug=self.kwargs['aggrement_slug']).first()
		return user_agreement

	def get_success_url(self, **kwargs):
		return reverse('aggrement:user_agreements_list')

	def form_valid(self, form):
		form.instance.is_draft = False
		return super(UpdateUserAggrement, self).form_valid(form)

	def get_context_data(self, **kwargs):
		context = super(UpdateUserAggrement, self).get_context_data(**kwargs)
		context['aggrement_details'] = self.get_object()
		return context



@login_required
def add_user_aggrement(request, aggrement_pk):
	"""
	Add user aggrement
	"""
	aggrement_details = Aggrement.objects.filter(pk=aggrement_pk).first()

	new_user_aggrement = UserAggrements.objects.create(user=request.user, aggrement=aggrement_details, textbody=aggrement_details.textbody,is_draft=True)
	new_user_aggrement.save()

	return redirect(reverse('aggrement:create_document' , kwargs={'aggrement_slug': new_user_aggrement.slug,}))

@login_required
def user_agreements_list(request):
	"""
	User Created Agreements List
	"""
	aggrements = UserAggrements.objects.filter(user=request.user, is_draft=False).order_by('-id')

	aggrement_category_list = AggrementCatagories.objects.all().order_by('id')

	page = request.GET.get('page', 1)
	paginator = Paginator(aggrements, 15)
	try:
		aggrement_list = paginator.page(page)
	except PageNotAnInteger:
		aggrement_list = paginator.page(1)
	except EmptyPage:
		aggrement_list = paginator.page(paginator.num_pages)

	context = {
		'aggrement_list' : aggrement_list,
		'aggrement_category_list' : aggrement_category_list
	}
	return CommonMixin.render(request, 'user_aggrement_list.html', context)

@login_required
def user_agreement_pdf(request, aggrement_slug):
	"""
	User Agreement Pdf
	"""
	user_agreement = UserAggrements.objects.filter(slug=aggrement_slug).first()

	context = {
		'user_agreement' : user_agreement
	}
	pdf = render_to_pdf('agreement_pdf.html', context)
	if pdf:
		return HttpResponse(pdf, content_type='application/pdf')
	else:
		messages.error(self.request, 'PDF Not Found')
		return HttpResponseRedirect(request.META.get("HTTP_REFERER"))


@login_required
def user_agreement_in_word(request, aggrement_slug):
	"""
	User Agreement In Word
	"""
	user_agreement = UserAggrements.objects.filter(slug=aggrement_slug).first()
	document = Document()

	document.add_heading(user_agreement.aggrement.title, 0)

	p = document.add_paragraph(user_agreement.aggrement.act, style='Intense Quote')

	agreement_html = render_to_string('agreement_in_word.html', {'user_agreement': user_agreement,})
	text_only = re.sub('[ \t]+', ' ', strip_tags(agreement_html))
	text_only.replace('\n ', '\n').strip()

	h = HTMLParser()
	
	document.add_paragraph(h.unescape(text_only))

	document.add_page_break()

	response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
	response['Content-Disposition'] = 'attachment; filename=' + user_agreement.slug + '.docx'

	document.save(response)

	return response

def agreement_share(request, aggrement_slug):
	"""
	Agreement Share Link
	"""
	user_agreement = UserAggrements.objects.filter(slug=aggrement_slug).first()

	context = {
		'user_agreement' : user_agreement
	}
	return CommonMixin.render(request, 'agreement_share.html', context)



def default_agreement_pdf_old(request, aggrement_slug): # not in use
	"""
	Default Agreement Pdf
	"""
 
	agreement_details = Aggrement.objects.filter(slug=aggrement_slug).first()
	agreement_details.download_pdf_count += 1 
	agreement_details.save()

	context = {
		'agreement_details' : agreement_details
	}
	pdf = render_to_pdf('default_agreement_pdf.html', context)
	if pdf:
		return HttpResponse(pdf, content_type='application/pdf')
	else:
		messages.error(request, 'PDF Not Found')
		return HttpResponseRedirect(request.META.get("HTTP_REFERER"))


def default_agreement_pdf(request):
	if request.method == 'POST':
		agreement_slug = request.POST.get('agreement_slug')
		agreement_details = Aggrement.objects.filter(slug=agreement_slug).first()
		if agreement_details:
			agreement_details.download_pdf_count += 1
			agreement_details.save()
			context = {
				'agreement_details': agreement_details
			}
			pdf = render_to_pdf('default_agreement_pdf.html', context)
			if pdf:
				pdf_base64 = base64.b64encode(pdf.getvalue()).decode('utf-8')
				return JsonResponse({'pdf': pdf_base64})
		return JsonResponse({'error': 'PDF Not Found'}, status=404)
	return JsonResponse({'error': 'Invalid Request'}, status=400)


def default_agreement_in_word_old(request, aggrement_slug): # not in use
	"""
	Default Agreement In Word
	"""
	agreement_details = Aggrement.objects.filter(slug=aggrement_slug).first()
	agreement_details.download_word_count += 1    # ===================== jasmeet added
	agreement_details.save()   # ===================== jasmeet added
	
	document = Document()

	document.add_heading(agreement_details.title, 0)

	p = document.add_paragraph(agreement_details.act, style='Intense Quote')

	agreement_html = render_to_string('default_agreement_in_word.html', {'agreement_details': agreement_details,})
	text_only = re.sub('[ \t]+', ' ', strip_tags(agreement_html))
	text_only.replace('\n ', '\n').strip()

	h = HTMLParser()
	
	document.add_paragraph(h.unescape(text_only))

	document.add_page_break()

	response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
	response['Content-Disposition'] = 'attachment; filename=' + agreement_details.title + '.docx'

	document.save(response)

	return response


def default_agreement_in_word(request):
	if request.method == 'POST':
		aggrement_slug = request.POST.get('agreement_slug')
		agreement_details = Aggrement.objects.filter(slug=aggrement_slug).first()
		if agreement_details:
			agreement_details.download_word_count += 1
			agreement_details.save()

			document = Document()
			document.add_heading(agreement_details.title, 0)
			p = document.add_paragraph(agreement_details.act, style='Intense Quote')

			agreement_html = render_to_string('default_agreement_in_word.html', {'agreement_details': agreement_details})
			text_only = re.sub('[ \t]+', ' ', strip_tags(agreement_html))
			text_only.replace('\n ', '\n').strip()

			h = HTMLParser()
			document.add_paragraph(h.unescape(text_only))
			document.add_page_break()

			# Saving Document to BytesIO
			buffer = BytesIO()
			document.save(buffer)
			buffer.seek(0)

			# Convert BytesIO content to base64 for JSON response
			base64_content = base64.b64encode(buffer.getvalue()).decode('utf-8')

			# Create JSON response
			response_data = {
				'success': True,
				'download_link': f'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,{base64_content}',
			}

			return JsonResponse(response_data)

	return JsonResponse({'error': 'Invalid request'})


def default_agreement_share(request, aggrement_slug):
	"""
	Agreement Share Link
	"""
	agreement_details = Aggrement.objects.filter(slug=aggrement_slug).first()

	context = {
		'agreement_details' : agreement_details
	}
	return CommonMixin.render(request, 'default_agreement_share.html', context)


class ScannedDocumentCreateView(LoginRequiredMixin, CommonMixin, CreateView):
	"""
	Scanned Document Create View
	"""
	form_class = ScannedUserAgreementsForm
	template_name = 'scan_agreements.html'

	def get_success_url(self, **kwargs):
		messages.success(self.request, 'Document Uploaded Successfully')
		return reverse('user_profile:profile_details')

	def form_valid(self, form):
		form.instance.user = self.request.user
		return super(ScannedDocumentCreateView, self).form_valid(form)


class UpdateScannedAggrement(LoginRequiredMixin, CommonMixin, UpdateView):
	"""
	User Aggrement Update View
	"""
	model = ScannedUserAgreements
	form_class = ScannedUserAgreementsForm
	template_name = 'scan_agreements.html'

	def get_object(self):
		scan_agreement = ScannedUserAgreements.objects.filter(slug=self.kwargs['scan_aggrement_slug']).first()
		return scan_agreement

	def get_success_url(self, **kwargs):
		messages.success(self.request, 'Document Updated Successfully')
		return reverse('user_profile:profile_details')

# from phonepe.sdk.pg.payments.payment_client import PhonePePaymentClient  
# from phonepe.sdk.pg.env import Env
# merchant_id = "PGTESTPAYUAT"  
# salt_key = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399"  
# salt_index = 1  
# env = Env.UAT # Change to Env.PROD when you go live
# def test1(request):
# 	phonepe_client = PhonePePaymentClient(merchant_id=merchant_id, salt_key=salt_key, salt_index=salt_index, env=env)


def make_base64(json_obj):
	json_str = json.dumps(json_obj, separators=(',', ':'))  # compact encoding
	return base64.urlsafe_b64encode(bytes(json_str, "utf-8")).decode("utf-8")


def make_hash(input_str):
	m = hashlib.sha256()
	m.update(input_str.encode())
	return m.hexdigest()


def make_request_body(base64_payload):
	request_body = {
		"request": base64_payload
	}
	data_json = json.dumps(request_body)
	return data_json



def check_user_status(request):
	user_authenticated = request.user.is_authenticated
	return JsonResponse({'user_authenticated': user_authenticated})


def goto_login(request):  
	# Retrieving data from the session
	agreement_slug = request.session.get('agreement_slug')   
	agreement_service_option = request.session.get('agreement_service_option')    
	# Reverse the URL with parameters
	checkout_url = reverse('aggrement:checkout', kwargs={'agreement_slug': agreement_slug, 'agreement_service_option': agreement_service_option})
	# Build the login URL with the 'next' parameter
	login_url = reverse('accounts:login')
	# Construct the 'next' URL with the reversed checkout URL and parameters
	next_url = f'{checkout_url}?next={checkout_url}'
	# Redirect to the login page with the 'next' parameter
	return redirect(f'{login_url}?next={next_url}')

def goto_signup(request):
	agreement_slug = request.session.get('agreement_slug')   
	agreement_service_option = request.session.get('agreement_service_option')    
	signUp_url = reverse('accounts:signup')
	# Reverse the URL with parameters
	checkout_url = reverse('aggrement:checkout', kwargs={'agreement_slug': agreement_slug, 'agreement_service_option': agreement_service_option})
	# Construct the 'next' URL with the reversed checkout URL and parameters
	next_url = f'{checkout_url}?next={checkout_url}'
	# Redirect to the login page with the 'next' parameter
	return redirect(f'{signUp_url}?next={next_url}')



def checkout(request, agreement_slug, agreement_service_option): 
	# Storing data in the session
	request.session['agreement_slug'] = agreement_slug
	request.session['agreement_service_option'] = agreement_service_option
	countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
	context = {}
	context['countries'] = countries
	context['states'] = states
	agreement_obj = Aggrement.objects.filter(slug=agreement_slug)
	
	if agreement_obj.exists():
		context['agreement'] = agreement_obj.first()
	context['agreement_service_option'] = agreement_service_option
	return render(request, 'checkout.html', context)

email_regex = r'^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$'
phone_regex = r'^\+?\d{1,3}\d{3,14}$'


def process_checkout(request):  # current works fine -> only one object with user and various guests but different set of phone, email
	
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			
			if data:
				country_object_value = None
				state_object_value = None
				city_value = None
				
				if 'country' in data:
					country_obj = CountryMaster.objects.filter(country_name=data["country"])
					if country_obj.exists():
						country_object_value = country_obj.first()
				if 'state' in data:
					state_obj = StateMaster.objects.filter(state_name=data["state"])
					if state_obj.exists():
						state_object_value = state_obj.first() 
				if 'city' in data:
					city_value = data["city"] 
				
				if request.user.is_authenticated:
					userOrder = UserOrder.objects.filter(user=request.user, phone_number=data["mobile"])
					if userOrder.exists():
						userOrder_obj = userOrder.first()
					else:
						userOrder_obj = UserOrder.objects.create(user=request.user, phone_number=data["mobile"])
					
					userOrder_obj.country = country_object_value
					userOrder_obj.state = state_object_value
					userOrder_obj.city = city_value
					userOrder_obj.save() 
					
					# save email , first_name, last_name from User
					if request.user.email:
						userOrder_obj.email = request.user.email
					if request.user.first_name:
						userOrder_obj.first_name = request.user.first_name
					if request.user.last_name:
						userOrder_obj.last_name = request.user.last_name
						
					profile_obj = Profile.objects.filter(user=request.user)
					if profile_obj.exists():
						profile = profile_obj.first()
						if not userOrder_obj.country:
							if profile.country:
								userOrder_obj.country = profile.country
						if not userOrder_obj.state:
							if profile.state:
								userOrder_obj.state = profile.state
						if not userOrder_obj.city:
							if profile.city:
								userOrder_obj.city = profile.city
								
					userOrder_obj.save()
						
					loggedInUserId = request.user.id
				else:
					loggedInUserId = 0
					userOrder = UserOrder.objects.filter(phone_number=data["mobile"], email=data["email"], user__isnull=True)

					if userOrder.exists():
						userOrder_obj = userOrder.first()
					else:
						userOrder_obj = UserOrder.objects.create(phone_number=data["mobile"], email=data["email"])
						
					if 'first_name' in data:
						userOrder_obj.first_name = data["first_name"]
						
					if 'last_name' in data:
						userOrder_obj.last_name = data["last_name"]
					
					userOrder_obj.country = country_object_value
					userOrder_obj.state = state_object_value
					userOrder_obj.city = city_value
					userOrder_obj.save() 

				if 'order_id' in data:
					orderAgreement = OrderAgreement.objects.filter(id=data['order_id'])
					if orderAgreement.exists():
						orderAgreement_obj = orderAgreement.first()
					
				else:
					orderAgreement_obj = OrderAgreement.objects.create(user=userOrder_obj)
					
				if 'accept_terms_conditions' in data: 
					if data['accept_terms_conditions'] == "on":
						orderAgreement_obj.agree_terms_conditions = True 
				if 'agreement_id' in data:
					a_id = int(data["agreement_id"])
					a_obj = Aggrement.objects.filter(id=a_id).first()
					orderAgreement_obj.agreement = a_obj 
					
					if 'agreement_service_option' in data:
						orderAgreement_obj.agreement_service_option = data["agreement_service_option"]
						# if data["agreement_service_option"] == "word":
						#     orderAgreement_obj.amount = a_obj.word_price 
						# elif data["agreement_service_option"] == "pdf":
						#     orderAgreement_obj.amount = a_obj.pdf_price
						# elif data["agreement_service_option"] == "make_with_ai":
						#     orderAgreement_obj.amount = a_obj.make_with_ai_price
						# elif data["agreement_service_option"] == "make_with_legal_help":
						#     orderAgreement_obj.amount = a_obj.make_with_legal_help_price
				
				if 'amount' in data:
					orderAgreement_obj.amount = int(data["amount"])
					
				if 'stamp_duty_amount' in data:
					orderAgreement_obj.stamp_duty_amount = int(data["stamp_duty_amount"])
				else:       
					orderAgreement_obj.stamp_duty_amount = None
				orderAgreement_obj.save()
				
				encoded_order_id = base64.urlsafe_b64encode(str(orderAgreement_obj.id).encode()).decode()
				to_complete_payment_url = redirect('aggrement:complete_pending_payment', order_id=encoded_order_id).url
				orderAgreement_obj.to_complete_payment_url = request.scheme + "://" + request.get_host() + to_complete_payment_url
				orderAgreement_obj.save()

				if 'order_id' in data:
					attempts = OrderAgreementRetryAttempt.objects.filter(order=orderAgreement_obj)
					if attempts.count() > 0:
						attempt_num = str(orderAgreement_obj.id) + "-A" + str(attempts.count()+1)
					else:    
						attempt_num = str(orderAgreement_obj.id) + "-A" + str(1)
					attempt_obj = OrderAgreementRetryAttempt.objects.create(order=orderAgreement_obj, attempt_number=attempt_num)
					merchantTransactionId = attempt_num
					merchantUserId = str(orderAgreement_obj.user.id)
					amount = orderAgreement_obj.amount
					mobileNumber = str(orderAgreement_obj.user.phone_number)

					# Order Reinitiated
					if orderAgreement_obj.user.email:
						user_email = orderAgreement_obj.user.email
					else:
						user_email = orderAgreement_obj.user.user.email
						
					
					url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[orderAgreement_obj.agreement.slug, orderAgreement_obj.agreement_service_option])
					
					# Create and start a thread to handle delayed payment status check and email sending to customer
					thread_reinitiated_pending_order_email_to_customer = threading.Thread(target=delayed_check_and_send_email_to_customer_for_reinitiated_and_pending_agreement_order, args=(orderAgreement_obj.id, user_email))
					thread_reinitiated_pending_order_email_to_customer.start()

					# Create and start a thread to handle delayed payment status check and email sending to admin
					thread_reinitiated_pending_order_email_to_admin = threading.Thread(target=delayed_check_and_send_email_to_admin_for_reinitiated_and_pending_agreement_order, args=(orderAgreement_obj.id, url_PlaceAgreementOrder))
					thread_reinitiated_pending_order_email_to_admin.start()

					encoded_attempt_num = base64.urlsafe_b64encode(attempt_num.encode()).decode()
					encoded_loggedInUserId = base64.urlsafe_b64encode(str(loggedInUserId).encode()).decode()
					order_id = orderAgreement_obj.id
					encoded_orderId = base64.urlsafe_b64encode(str(order_id).encode()).decode()
					redirectUrl_check_status = request.scheme + "://" + request.get_host() +  reverse('aggrement:check_status_order_attempt_complete_payment', kwargs={'logged_in_user_id': encoded_loggedInUserId, 'order_id': encoded_orderId, 'attempt_number': encoded_attempt_num})  # encoded_loggedInUserId  o_id #reverse('aggrement:success_checkout')
					response = make_pay_page_request(merchantTransactionId=merchantTransactionId, merchantUserId=merchantUserId, amount=amount*100, mobileNumber=mobileNumber, redirectUrl=redirectUrl_check_status)
				
					if response.status_code == 200:
						res = response.json()
						orderAgreement_obj.payment_status = res["code"]
						orderAgreement_obj.save()
						attempt_obj.payment_status = res["code"]
						attempt_obj.save()
				
						if res['success'] == True:
							redirectURL = res['data']['instrumentResponse']['redirectInfo']['url']
							return JsonResponse({'success': res["message"], 'redirect_url' : redirectURL})
						else:
							return JsonResponse({'error': res["message"]})
					else:
						messages.error(request, "Payment request failed. Try Again")
						agreement_slug = orderAgreement_obj.agreement.slug   
						agreement_service_option = orderAgreement_obj.agreement_service_option  
						return reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])
				
				else:
					o_id = orderAgreement_obj.id 
					encoded_order_id = base64.urlsafe_b64encode(str(o_id).encode()).decode()
					encoded_loggedInUserId = base64.urlsafe_b64encode(str(loggedInUserId).encode()).decode()
				
					merchantTransactionId = str(o_id)
					
					merchantUserId = str(orderAgreement_obj.user.id)
					
					amount = orderAgreement_obj.amount
					mobileNumber = str(orderAgreement_obj.user.phone_number)
	
					# New Order Placed
					if orderAgreement_obj.user.email:
						user_email = orderAgreement_obj.user.email
					else:
						user_email = orderAgreement_obj.user.user.email
					
					url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[orderAgreement_obj.agreement.slug, orderAgreement_obj.agreement_service_option])
					
					# Create and start a thread to handle delayed payment status check and email sending to customer
					thread_pending_order_email_to_customer = threading.Thread(target=delayed_check_and_send_email_to_customer_for_placed_and_pending_agreement_order, args=(orderAgreement_obj.id, user_email))
					thread_pending_order_email_to_customer.start()

					# Create and start a thread to handle delayed payment status check and email sending to admin
					thread_pending_order_email_to_admin = threading.Thread(target=delayed_check_and_send_email_to_admin_for_placed_and_pending_agreement_order, args=(orderAgreement_obj.id, url_PlaceAgreementOrder))
					thread_pending_order_email_to_admin.start()
    
					redirectUrl_check_status = request.scheme + "://" + request.get_host() +  reverse('aggrement:check_status_order', kwargs={'logged_in_user_id': encoded_loggedInUserId, 'order_id': encoded_order_id})  # encoded_loggedInUserId  o_id #reverse('aggrement:success_checkout')
					
					response = make_pay_page_request(merchantTransactionId=merchantTransactionId, merchantUserId=merchantUserId, amount=amount*100, mobileNumber=mobileNumber, redirectUrl=redirectUrl_check_status)
					
					if response.status_code == 200:
						res = response.json()
						orderAgreement_obj.payment_status = res["code"]
						orderAgreement_obj.save()
						if res['success'] == True:
							redirectURL = res['data']['instrumentResponse']['redirectInfo']['url']
							return JsonResponse({'success': res["message"], 'redirect_url' : redirectURL})
						else:
							return JsonResponse({'error': res["message"]})
					else:
						messages.error(request, "Payment request failed. Try Again")
						agreement_slug = orderAgreement_obj.agreement.slug   
						agreement_service_option = orderAgreement_obj.agreement_service_option  
						return reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])
					
		except Exception as e:
			print(f"Exception while processing checkout:  {e}")
			return JsonResponse({'success': False, 'error': str(e)})



def payment_initiate(request):
	o_id = request.session.get('order_id')
	merchantTransactionId = str(o_id)
	# merchantTransactionId = str(order_id)
	# order_obj = OrderAgreement.objects.filter(id=o_id).first()
	# print("=========== order obj : ",order_obj.id)
	# print("=========== order user id : ",order_obj.user.id)
	merchantUserId = str(request.session.get('userOrder_id'))
	# merchantUserId = str(order_obj.user.id)
	amount = request.session.get('amount')
	mobileNumber = str(request.session.get('mobile'))
	# mobileNumber = order_obj.user.phone_number
	redirectUrl_check_status = request.scheme + "://" + request.get_host() +  reverse('aggrement:check_status', args=[o_id])  #reverse('aggrement:success_checkout')
	# print(f"=============== MTID : {merchantTransactionId}, User : {merchantUserId}, amt : {amount}, mobile:  {mobileNumber}")
	# print(f"=============== MTID type : {type(merchantTransactionId)}, User : {type(merchantUserId)}, amt : {type(amount)}, mobile:  {type(mobileNumber)}")
	
	# response = make_pay_page_request(merchantTransactionId="1001", merchantUserId="11", amount=1*100, mobileNumber="9733025982", redirectUrl=redirectUrl_check_status)
	response = make_pay_page_request(merchantTransactionId=merchantTransactionId, merchantUserId=merchantUserId, amount=amount*100, mobileNumber=mobileNumber, redirectUrl=redirectUrl_check_status)
	# print("==================== response initiate : ", response)

	# if response.status_code == 200:
	#     # print("Payment request successful.")
	#     res = response.json()
		
	#     if res['success']: # check
			
	#         redirectURL = res['data']['instrumentResponse']['redirectInfo']['url']
		   
	#         print("============= redirect UR: ",redirectURL)
	#         return redirect(redirectURL)
	# else:
	#     messages.error(request, "Payment request failed. Try Again")
	#     return redirect("aggrement:aggrementlist")  #  ================ previous
			
 
 
	if response.status_code == 200:
		# print("Payment request successful.")
		res = response.json()
		# print("res ============ json: ", res)
		order_obj = OrderAgreement.objects.filter(id=merchantTransactionId).first()
		order_obj.payment_status = res["code"]
		order_obj.save()
		# print("============ payment_status saved in order")
		if res['success']: # check
			redirectURL = res['data']['instrumentResponse']['redirectInfo']['url']
			# print("=============== redirect to redirectURL ")
			return redirect(redirectURL)
	else:
		messages.error(request, "Payment request failed. Try Again")
		# return redirect("aggrement:aggrementlist")  #  ================ previous
		agreement_slug = request.session.get('agreement_slug')   
		agreement_service_option = request.session.get('agreement_service_option')    
		return reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])





@csrf_exempt
def check_status_order(request, logged_in_user_id, order_id):  #logged_in_user_id
	
	if logged_in_user_id: 
		encoded_loggedInUserId = logged_in_user_id

	encoded_order_id = order_id
	# Decode the encoded order_id
	decoded_order_id = int(base64.urlsafe_b64decode(encoded_order_id.encode()).decode())
	
	merchantTransactionId = str(decoded_order_id)
	
	response = make_status_request(merchantTransactionId)
	
	order_obj = OrderAgreement.objects.filter(id=merchantTransactionId).first()

	if response.status_code == 200:
		res = response.json()
		# Process the response data as needed
		order_obj.payment_status = res["code"]
		order_obj.save()
		if order_obj.user.email:
			user_email = order_obj.user.email
		else:
			user_email = order_obj.user.user.email
   
		url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[order_obj.agreement.slug, order_obj.agreement_service_option])
				
		if res["code"] == "PAYMENT_SUCCESS":
	
			if res["data"]["paymentInstrument"]["type"] == "CARD":
				order_obj.payment_mode = res["data"]["paymentInstrument"]["cardType"]
			else:
				order_obj.payment_mode = res["data"]["paymentInstrument"]["type"]
			
			redirectUrl_order_detail = request.scheme + "://" + request.get_host() +  reverse('aggrement:get_completed_order_detail', kwargs={'logged_in_user_id': encoded_loggedInUserId, 'order_id': encoded_order_id})  # encoded_loggedInUserId  o_id #reverse('aggrement:success_checkout')
			order_obj.payment_completed_url = redirectUrl_order_detail
			order_obj.to_complete_payment_url = ""
			order_obj.save()

			if order_obj.agreement_service_option == "word" or order_obj.agreement_service_option == "pdf":
				order_obj.order_status = "COMPLETED" 
				order_obj.save()
				thread = threading.Thread(target=send_email_for_completed_word_or_pdf_agreement_order_with_successful_payment_completed_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
				thread.start()
			else:
				thread = threading.Thread(target=send_email_for_agreement_order_with_successful_payment_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
				thread.start()
    
			return redirect('aggrement:get_completed_order_detail', logged_in_user_id=encoded_loggedInUserId, order_id=encoded_order_id)

		else:
			
			thread = threading.Thread(target=send_email_for_payment_failed_agreement_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
			thread.start()
			encoded_order_id = base64.urlsafe_b64encode(str(order_obj.id).encode()).decode()
			return redirect('aggrement:complete_pending_payment', order_id=encoded_order_id)
	
	else:
		
		messages.error(request, "Check status request failed at this moment. Please try again in a while.")
		agreement_slug = order_obj.agreement.slug 
		agreement_service_option = order_obj.agreement_service_option  
		return reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])


def get_completed_order_detail(request, logged_in_user_id, order_id):
	if logged_in_user_id:
		encoded_loggedInUserId = logged_in_user_id
		decoded_loggedInUserId = int(base64.urlsafe_b64decode(encoded_loggedInUserId.encode()).decode())
		if decoded_loggedInUserId > 0:
			user_obj = User.objects.filter(id=decoded_loggedInUserId).first()
			login(request, user_obj)
	
	encoded_order_id = order_id
	# Decode the encoded order_id
	decoded_order_id = int(base64.urlsafe_b64decode(encoded_order_id.encode()).decode())
	merchantTransactionId = str(decoded_order_id)
	order_obj = OrderAgreement.objects.filter(id=merchantTransactionId).first()
	
	context = {
		'order' : order_obj
	}  
	return render(request, 'payment_done.html', context)


@csrf_exempt
def check_status_order_attempt_complete_payment(request, logged_in_user_id, order_id, attempt_number):  #logged_in_user_id
	encoded_order_id = order_id
	if logged_in_user_id:
		encoded_loggedInUserId = logged_in_user_id
		
	
	if attempt_number:
		encoded_attempt_num = attempt_number
		# Decode the encoded attempt_num
		decoded_attempt_num = base64.urlsafe_b64decode(encoded_attempt_num.encode()).decode()
		merchantTransactionId = decoded_attempt_num
		response = make_status_request(merchantTransactionId)  # "MT7850590068188104"
		attempt_obj = OrderAgreementRetryAttempt.objects.filter(attempt_number=decoded_attempt_num).first()
		order_obj = OrderAgreement.objects.filter(id=attempt_obj.order.id).first()
		if response.status_code == 200:
			res = response.json()
			
			attempt_obj.payment_status = res["code"]
			attempt_obj.save()
			
			order_obj.payment_status = res["code"]
			order_obj.save()
			if order_obj.user.email:
				user_email = order_obj.user.email
			else:
				user_email = order_obj.user.user.email
    
			url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[order_obj.agreement.slug, order_obj.agreement_service_option])
    
			if res["code"] == "PAYMENT_SUCCESS":
				
				if res["data"]["paymentInstrument"]["type"] == "CARD":
					order_obj.payment_mode = res["data"]["paymentInstrument"]["cardType"]
				else:
					order_obj.payment_mode = res["data"]["paymentInstrument"]["type"]
				
				# redirectUrl_check_status = request.scheme + "://" + request.get_host() +  reverse('aggrement:check_status_order_attempt_complete_payment', kwargs={'logged_in_user_id': encoded_loggedInUserId, 'order_id': encoded_order_id, 'attempt_number': encoded_attempt_num})  # encoded_loggedInUserId  o_id #reverse('aggrement:success_checkout')
				redirectUrl_order_detail = request.scheme + "://" + request.get_host() +  reverse('aggrement:get_completed_order_detail', kwargs={'logged_in_user_id': encoded_loggedInUserId, 'order_id': encoded_order_id})  # encoded_loggedInUserId  o_id #reverse('aggrement:success_checkout')
				order_obj.payment_completed_url = redirectUrl_order_detail
				order_obj.to_complete_payment_url = ""
				order_obj.save()
				
    
				if order_obj.agreement_service_option == "word" or order_obj.agreement_service_option == "pdf":
					order_obj.order_status = "COMPLETED" 
					order_obj.save()
					
					thread = threading.Thread(target=send_email_for_completed_reinitiated_word_or_pdf_agreement_order_with_successful_payment_completed_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
					thread.start()
				
				else:
					thread = threading.Thread(target=send_email_for_reinitiated_agreement_order_with_successful_payment_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
					thread.start()

				return redirect('aggrement:get_completed_order_detail', logged_in_user_id=encoded_loggedInUserId, order_id=encoded_order_id)
			else:
				thread = threading.Thread(target=send_email_for_payment_failed_agreement_order, args=(order_obj, user_email, url_PlaceAgreementOrder))
				thread.start()
				return redirect('aggrement:complete_pending_payment', order_id=encoded_order_id)
		
		else:
			messages.error(request, "Check status request failed at this moment. Please try again in a while.")
			agreement_slug = order_obj.agreement.slug 
			agreement_service_option = order_obj.agreement_service_option  
			return reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])
	


def payment_done(request, order_id):
	order_obj = OrderAgreement.objects.filter(id=order_id).first()
	context = {
		'agreement_service_option': order_obj.agreement_service_option,	
		'agreement_slug': order_obj.agreement.slug 
	}
	return render(request, 'payment_done.html', context)


def download_paid_agreement_in_word(request, order_id):
	order_obj = OrderAgreement.objects.filter(id=order_id).first()
	order_obj.download_word_count += 1
	order_obj.save()
	return redirect('aggrement:main_agreement_in_word', aggrement_slug=order_obj.agreement.slug)


def download_paid_agreement_in_pdf(request, order_id):
	order_obj = OrderAgreement.objects.filter(id=order_id).first()
	order_obj.download_pdf_count += 1
	order_obj.save()
	return redirect('aggrement:main_agreement_pdf', aggrement_slug=order_obj.agreement.slug)
	

def checkout_terms_conditions(request):
	return render(request, 'checkout_terms_conditions.html')


class CustomEncoder(json.JSONEncoder):
	def default(self, obj):
		if isinstance(obj, Aggrement):
			return obj.isoformat()  # Convert datetime objects to ISO format
		# Add other custom serialization logic for any other non-serializable types here
		return super().default(obj)



def get_category_agreements(request):
	if request.method == 'POST':
		agreement_category_id = request.POST.get('agreement_category_id')  # Access the value of 'a' sent from the AJAX request
		ag_cat_obj = AggrementCatagories.objects.filter(id=agreement_category_id).first()
		agreement_qs = ag_cat_obj.aggrements.all()
		agreement_list = list(agreement_qs.values())  # Convert QuerySet to a list of dictionaries
		return JsonResponse({'agreement_list': agreement_list}, safe=False)


def get_agreement_detail(request):
	if request.method == 'POST':
		agreement_slug = request.POST.get('agreement_slug') 
		agreement_obj = Aggrement.objects.filter(slug=agreement_slug).values().first()
		return JsonResponse({'agreement_detail': agreement_obj})
	

def make_with_ai(request, agreement_slug):
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			if data:
				# check if with email user exists or not
				userAI = UserMakeAgreementWithAI.objects.filter(email=data["email"])
				if userAI.exists():
					userAI_obj = userAI.first()
				else:
					userAI_obj = UserMakeAgreementWithAI() 
					if 'email' in data:
						# pass
						if re.match(email_regex, data["email"]):
							userAI_obj.email=data["email"]  
					if 'first_name' in data:
						# pass
						userAI_obj.first_name = data["first_name"] 
					if 'last_name' in data:
						# pass
						userAI_obj.last_name = data["last_name"]     
					if 'country' in data:
						# pass
						country_obj = CountryMaster.objects.filter(country_name=data["country"])
						if country_obj.exists():
							userAI_obj.country = country_obj.first()  
					if 'state' in data:
						# pass
						state_obj = StateMaster.objects.filter(state_name=data["state"])
						if state_obj.exists():
							userAI_obj.state = state_obj.first() 
					if 'city' in data:
						# pass
						userAI_obj.city = data["city"] 
					if 'mobile' in data:
						if re.match(phone_regex, data["mobile"]):  
							# pass
							userAI_obj.phone_number = data["mobile"]    
					userAI_obj.save() 
					
				request.session['userMakeAgreementWithAI_id'] = userAI_obj.id

				return JsonResponse({'success': True})
		except Exception as e:
			return JsonResponse({'success': False, 'error': str(e)})
	agreement_details = Aggrement.objects.filter(slug=agreement_slug).first()
	
	context = {
		'agreement_details' : agreement_details
	}
	if request.user.is_authenticated:
		user_AI = UserMakeAgreementWithAI.objects.filter(user=request.user)
		if user_AI.exists():
			userAI_obj = user_AI.first()
		else:    
			userAI_obj = UserMakeAgreementWithAI.objects.create(user=request.user) 
		request.session['userMakeAgreementWithAI_id'] = userAI_obj.id
	request.session['agreement_id'] = agreement_details.id
	return render(request, 'make_with_ai.html', context)
	
	

# def chunk_text(text):
#     chunk_size = 4096
#     tokens = openai.Tokenizer.tokenize(text)
#     chunks = []
#     current_chunk = []
	
#     for token in tokens:
#         current_chunk.append(token)
#         if sum(len(t) for t in current_chunk) >= chunk_size:
#             chunks.append("".join(current_chunk))
#             current_chunk = []

#     if current_chunk:
#         chunks.append("".join(current_chunk))

#     return chunks


# def process_chunks(chunks):
#     results = []

#     for chunk in chunks:
#         response = openai.Completion.create(engine="text-davinci-003", prompt=chunk, max_tokens=100)
#         results.append(response.choices[0].text.strip())

#     return results


def chatbot(request):
	conversation = request.session.get('conversation', [])
	if request.method == 'POST':
		user_input = request.POST.get('user_input')
		openai.api_key = OPEN_AI_KEY
		
		# response = query.choices[0].text  previous
		# response = query.choices[0].text.splitlines()
		
		# response = openai.Completion.create(   # it works, below is output
		#     engine="text-davinci-003",
		#     prompt=user_input,
		#     # max_tokens=100,
		#     # temperature=0.6,
		#     # n=1,
		#     # stop=None
		#     temperature= 1, # trying
		#     max_tokens= 256,  # trying
		#     top_p= 1.0,  # trying
		#     frequency_penalty= 0.0,
		#     presence_penalty= 0.0
		#     # stop= ["\"\"\""],
		# )
		# r = response.choices[0].text.strip()
		
		# response output
		#         or the award. 

		# List of items to be entered:
		# - Shri…………………………, sole arbitrator
		# - Books, deeds, papers, vouchers, registers and documents
		# - The cost of and incidental to the reference and award
		# - The Arbitration and Conciliation Act, 1996 
		# - Six months 
		# - Fraud, collusion or error apparent on the face of the award


		response = openai.Completion.create(  # it works better - below response :
			model="text-davinci-003",
			prompt= user_input,
			temperature=0.7,
			max_tokens=2049,  # 400 to be later set to maximum to avg. of conversation after seeing avg. tokens
			top_p=1,
			frequency_penalty=0,
			presence_penalty=0
		)
		r = response.choices[0].text
		#         ========================================== response :  .

		# List of items to be entered: 
		# -Interpretation of the agreement 
		# -Rights, duties and liabilities of the parties 
		# -Delivery or non-delivery of orders by the principal 
		# -Damage or loss occurring to any of the parties 
		# -Fulfillment of the obligations of the parties 
		# -Matter arising out or in connection with the agreement 
		# -Books, deeds, papers, vouchers, registers and documents 
		# -Arbitrator 
		# -Cost of and incidental to the reference and award 
		# -Arbitration and Conciliation Act, 1996 
		# -Time for making the award 
		# -Final, conclusive and binding award 
		# -Grounds for challenging the award
		
		# conversation = completion
		# Extract chatbot replies from the response

		# chatbot_replies = [message['message']['content'] for message in response['choices'] if message['message']['role'] == 'assistant']
		
		# Append chatbot replies to the conversation
		# for reply in chatbot_replies:
		#     conversation.append({"role": "assistant", "content": reply})

		# Update the conversation in the session
		# request.session['conversation'] = conversation
		# print("======================================= chatbot_replies : ",chatbot_replies)
		# print("======================================= conversation : ",conversation)
		# return render(request, 'chat.html', {'user_input': user_input, 'chatbot_replies': chatbot_replies, 'conversation': conversation})
		# return JsonResponse({'user_input': user_input, 'chatbot_replies': chatbot_replies, 'conversation': conversation})
		return JsonResponse({'response': r})  # previous variable - response
	else:
		request.session.clear()
		# return render(request, 'chat.html', {'conversation': conversation})
		return JsonResponse({'conversation': conversation})


def save_chat(request):
	agreement_id = request.session.get('agreement_id', None)
	agreement_obj = Aggrement.objects.filter(id=agreement_id).first()
	user_id = request.session.get('userMakeAgreementWithAI_id', None)
	user_obj = UserMakeAgreementWithAI.objects.filter(id=user_id).first()
	
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			if data:
				chat_data = data.get('chat_data', [])
				if chat_data:
					# get or create Chat model object
					chat_obj, created = Chat.objects.get_or_create(user=user_obj, agreement=agreement_obj)
					
					for entry in chat_data:
						user_type = entry.get('user_type', 'None')
						date_time = entry.get('date_time', 'None')
						message = entry.get('message', 'None')
						# Convert the string to a datetime object
						date_time_obj = datetime.strptime(date_time, "%m/%d/%Y, %I:%M:%S %p")
						# Make sure to set the timezone if needed
						# Assuming your Django project is set to use a specific timezone, you can set it as follows:
						timezone_aware_obj = timezone.make_aware(date_time_obj, timezone.get_current_timezone())

						# create UserBotMessage model object
						userBotMessage_obj = UserBotMessage.objects.create(sender=user_type, content=message, timestamp=timezone_aware_obj)
						
						# add UserBotMessage model object in Chat model object
						chat_obj.user_bot_messages.add(userBotMessage_obj)
						chat_obj.save()
						
				downloaded_word_responses = data.get('downloaded_word_responses', [])
				if downloaded_word_responses:
					# get or create UserDownloadAIWord model object
					userDownloadAIWord_obj, created = UserDownloadAIWord.objects.get_or_create(user=user_obj, agreement=agreement_obj)
					
					for entry in downloaded_word_responses:
						date_time = entry.get('date_time', 'None')
						message = entry.get('message', 'None')
						
						# Convert the string to a datetime object
						date_time_obj = datetime.strptime(date_time, "%m/%d/%Y, %I:%M:%S %p")
						# Make sure to set the timezone if needed
						# Assuming your Django project is set to use a specific timezone, you can set it as follows:
						timezone_aware_obj = timezone.make_aware(date_time_obj, timezone.get_current_timezone())

						# create DownloadAIResponse model object
						
						downloadAIResponse_obj = DownloadAIResponse.objects.create(content=message, timestamp=timezone_aware_obj)
						
						# add DownloadAIResponse model object in UserDownloadAIWord model object
						userDownloadAIWord_obj.downloads.add(downloadAIResponse_obj)
						userDownloadAIWord_obj.save()
						
				downloaded_pdf_responses = data.get('downloaded_pdf_responses', [])
				if downloaded_pdf_responses:
					# get or create UserDownloadAIPdf model object
					userDownloadAIPdf_obj, created = UserDownloadAIPdf.objects.get_or_create(user=user_obj, agreement=agreement_obj)
					
					for entry in downloaded_pdf_responses:
						date_time = entry.get('date_time', 'None')
						message = entry.get('message', 'None')
						
						# Convert the string to a datetime object
						date_time_obj = datetime.strptime(date_time, "%m/%d/%Y, %I:%M:%S %p")
						# Make sure to set the timezone if needed
						# Assuming your Django project is set to use a specific timezone, you can set it as follows:
						timezone_aware_obj = timezone.make_aware(date_time_obj, timezone.get_current_timezone())

						# create DownloadAIResponse model object
						downloadAIResponse_obj = DownloadAIResponse.objects.create(content=message, timestamp=timezone_aware_obj)
						
						# add DownloadAIResponse model object in UserDownloadAIPdf model object
						userDownloadAIPdf_obj.downloads.add(downloadAIResponse_obj)
						userDownloadAIPdf_obj.save()
				# request.session.clear()
				return JsonResponse({'success': True})
		except Exception as e:
			return JsonResponse({'success': False, 'error': str(e)})




def complete_pending_payment(request, order_id): #  agreement_slug, agreement_service_option
	# print("============ checkout payment failed view start")
	
	# =====================Added ==========================
	encoded_order_id = order_id
	# Decode the encoded order_id
	decoded_order_id = int(base64.urlsafe_b64decode(encoded_order_id.encode()).decode())
	order_obj = OrderAgreement.objects.filter(id=decoded_order_id).first()  # 20 
	agreement_slug = order_obj.agreement.slug
	agreement_service_option = order_obj.agreement_service_option
	#========================================================
	# Storing data in the session
	request.session['agreement_slug'] = agreement_slug
	request.session['agreement_service_option'] = agreement_service_option
	countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
	context = {}
	context['countries'] = countries
	context['states'] = states
	agreement_obj = Aggrement.objects.filter(slug=agreement_slug)
	
	if agreement_obj.exists():
		context['agreement'] = agreement_obj.first()
	context['agreement_service_option'] = agreement_service_option
	
	# =========================== Added ==================================
	# order_obj = OrderAgreement.objects.filter(id=20).first()   # 105- pdf  # 17 - word   # 20 - without user and word
	context['order_obj'] = order_obj 
	if order_obj.user.user:
		user_id = order_obj.user.user.id 
		user = User.objects.get(id=user_id)
		login(request, user)
	else:
		# Log out the user
		logout(request)
	return render(request, 'checkout.html', context)




# =======================   extra added ======================================


def get_agreement_with_fixed_stamp_duty(request):
	if request.method == 'POST':
		data = json.loads(request.body.decode('utf-8'))
		fixed_object = AgreementWithFixedStampDutyInAllStates.objects.filter(agreement__slug=data["agreement_slug"])
		
		if fixed_object.exists():
			fixed_object_values = fixed_object.values('stamp_duty__amount').first()
			response_data = {
				'success': True,
				'data': fixed_object_values
			}
			return JsonResponse(response_data)
	return JsonResponse({'error': 'Invalid request'})


def get_agreement_with_different_stamp_duty(request):
	if request.method == 'POST':
		data = json.loads(request.body.decode('utf-8'))
		different_object = AgreementWithDifferentStampDutyInAllStates.objects.filter(agreement__slug=data["agreement_slug"], state__state_name=data["stamp_duty_state"])
		if different_object.exists():
			different_object_values = different_object.values('state__state_name', 'stamp_duty__amount').first()
			response_data = {
				'success': True,
				'data': different_object_values
			} 
			return JsonResponse(response_data)
	return JsonResponse({'error': 'Invalid request'})



def check_e_stamp_paper_with_state(request):
	is_found = False
	if request.method == 'POST':
		state_name = request.POST.get('state_name')
		e_stamp_paper_object = EStampPaperState.objects.filter(state_name=state_name, is_e_stamp_paper_allowed=True)
		if e_stamp_paper_object.exists():
			is_found = True
		response_data = {
			'result': is_found
		} 
		return JsonResponse(response_data)
	return JsonResponse({'error': 'Invalid request'})



def get_most_used_legal_agreements(request):
	"""
		Most used legal agreements View
	"""
	
	# questions = FAQ.objects.filter(service=service).order_by('id')
	# Assuming Order has a ForeignKey to Product
	most_ordered = Aggrement.objects.annotate(
		order_count=Count('agreement_orderAgreements')
	).filter(order_count__gt=0).order_by('-order_count')

	page = request.GET.get('page', 1)

	paginator = Paginator(most_ordered, 20) # 12

	try:
		most_ordered_list = paginator.page(page)
	except PageNotAnInteger:
		most_ordered_list = paginator.page(1)
	except EmptyPage:
		most_ordered_list = paginator.page(paginator.num_pages)

	context={
		'most_ordered_agreements' : most_ordered_list
	}
	
	return render(request,"most_used_agreements.html",context)


def is_email_or_phone_already_exist_for_UserLookingForAgreement(request):
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			if data:
				email = data['email']
				phone_no = data['phone']
				
				if email and phone_no:
					existing_contact_same_email_different_phone = UserLookingForAgreement.objects.exclude(phone_number=phone_no).filter(email=email).exists()
					if existing_contact_same_email_different_phone:
						return JsonResponse({"is_exist": True, "msg": "A request with this email already exists, but with a different phone number. Please enter correct phone number."})
						
					existing_contact_different_email_same_phone = UserLookingForAgreement._meta.model.objects.exclude(email=email).filter(phone_number=phone_no).exists()
					if existing_contact_different_email_same_phone:
						return JsonResponse({"is_exist": True, "msg": "A request with this phone number already exists, but with a different email. Please enter correct email."})
			
					if not existing_contact_same_email_different_phone and not  existing_contact_different_email_same_phone:
						return JsonResponse({"is_exist": False, "msg": ""})
			
		except Exception as e:
			print(f"Exception while processing form submission:  {e}")
			return JsonResponse({'error': str(e)})
	else:
		# Invalid request method
		return JsonResponse({'error': 'Invalid request method.'}, status=405)


def send_email_Looking_for_agreement(obj, user_email, url_LookingForAgreement):
	# send email to customer
	send_agreement_order_email(obj, user_email, 'LOOKING-FOR-AGREEMENT-REQUEST-ACCEPTED-MAIL')
	
	# send email to admin   
	send_agreement_order_email(obj, ADMIN_EMAIL, 'ADMIN-LOOKING-FOR-AGREEMENT-REQUEST-ARRIVED-MAIL', url_LookingForAgreement)


def looking_for_agreement(request):
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			if data:
				email = data['email']
				phone_number = data['phone']
				name = data['name']

				# Begin reCAPTCHA validation
				recaptcha_response = data['grecaptcha_response']
				url = 'https://www.google.com/recaptcha/api/siteverify'
				values = {
					'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
					'response': recaptcha_response
				}
	
				r = requests.post(url, data=values)
				verification_result = r.json()
				
				if verification_result.get('success'):
				
					user_objects = UserLookingForAgreement.objects.filter(phone_number=phone_number, email=email)
					
					if user_objects.exists():
						user = user_objects.first()
					else:
						user = UserLookingForAgreement(phone_number=phone_number, email=email)
					user.name = name
					user.save()
					
					obj = LookingForAgreement.objects.create(user=user, agreement_title=data['agreement_title'])
					url_LookingForAgreement = request.scheme + "://" + request.get_host() +  reverse('aggrement:aggrementlist')
					thread = threading.Thread(target=send_email_Looking_for_agreement, args=(obj, user.email, url_LookingForAgreement))
					thread.start()

					return JsonResponse({'success': True, 'request_id': obj.id})
				else:
					# reCAPTCHA verification failed
					return JsonResponse({'error': 'reCAPTCHA verification failed. Please try again.'})
					
		except Exception as e:
			print(f"Exception while processing form submission:  {e}")
			return JsonResponse({'error': str(e)})
	else:
		# Invalid request method
		return JsonResponse({'error': 'Invalid request method.'}, status=405)



def is_email_or_phone_already_exist_for_UserAgreementOrder(request):
	if request.method == 'POST' and request.is_ajax():
		try:
			data = json.loads(request.body.decode('utf-8'))
			if data:
				if 'email' in data:
					email = data['email']
				else:
					email = request.user.email
				phone_no = data['mobile']
				
				if email and phone_no:
					existing_contact_same_email_different_phone = UserOrder.objects.exclude(phone_number=phone_no).filter(email=email).exists()
					if existing_contact_same_email_different_phone:
						return JsonResponse({"is_exist": True, "msg": "A request with this email already exists, but with a different phone number. Please enter correct phone number."})
						
					existing_contact_different_email_same_phone = UserOrder._meta.model.objects.exclude(email=email).filter(phone_number=phone_no).exists()
					if existing_contact_different_email_same_phone:
						return JsonResponse({"is_exist": True, "msg": "A request with this phone number already exists, but with a different email. Please enter correct email."})
			
					if not existing_contact_same_email_different_phone and not  existing_contact_different_email_same_phone:
						return JsonResponse({"is_exist": False, "msg": ""})
			
		except Exception as e:
			print(f"Exception while processing form submission:  {e}")
			return JsonResponse({'error': str(e)})
	else:
		# Invalid request method
		return JsonResponse({'error': 'Invalid request method.'}, status=405)


# Function to get earnings for every 12 months
def get_earnings_for_year():
	# Get the current date
	current_date = timezone.now() #datetime.datetime.now()
	
	# Calculate the start date 12 months ago
	start_date = current_date - dt.timedelta(days=365)
	
	# Initialize dictionary to store earnings for each month
	earnings_by_month = {}
	
	# Loop through each month within the last 12 months
	while start_date < current_date:
		# Calculate the end date of the month
		end_date = start_date.replace(day=1) + dt.timedelta(days=32)
		end_date = end_date.replace(day=1) - dt.timedelta(days=1)
		
		# Query earnings for the current month
		earnings = OrderAgreement.objects.filter(
			created_at__range=[start_date, end_date], payment_status="PAYMENT_SUCCESS"
		).aggregate(total_earnings=Sum('amount'))['total_earnings'] or 0
		
		# Store earnings for the current month
		earnings_by_month[start_date.strftime('%B %Y')] = earnings
		
		# Move to the next month
		start_date = end_date + dt.timedelta(days=1)
	
	return earnings_by_month



def get_agreement_order_count(option): # completed orders count
	count = OrderAgreement.objects.filter(
		agreement_service_option=option, payment_status="PAYMENT_SUCCESS"
	).count()
	return count


def get_agreement_order_total_amount(option):
	total_amount = OrderAgreement.objects.filter(
		agreement_service_option=option,
		payment_status="PAYMENT_SUCCESS"
	).aggregate(total=Sum('amount'))['total'] or 0
	return total_amount


def get_agreement_pending_order_count(option):
	count = OrderAgreement.objects.filter(
		order_status="PENDING" # Q(payment_status="PAYMENT_INITIATED") | Q(payment_status="PAYMENT_ERROR")
		, agreement_service_option=option
	).count()
	return count


def get_agreement_pending_order_amount(option):
	total_amount = OrderAgreement.objects.filter(
		agreement_service_option=option,
		order_status="PENDING"
	).aggregate(total=Sum('amount'))['total'] or 0
	return total_amount


def get_agreement_failed_order_count(option):
	count = OrderAgreement.objects.filter(
		agreement_service_option=option, payment_status="PAYMENT_ERROR"
	).count()
	return count


def get_agreement_failed_order_amount(option):
	total_amount = OrderAgreement.objects.filter(
		agreement_service_option=option,
		payment_status="PAYMENT_ERROR"
	).aggregate(total=Sum('amount'))['total'] or 0
	return total_amount


def get_total_orders_count(): 
	orders_word_count = get_agreement_order_count("word") 
	orders_pdf_count = get_agreement_order_count("pdf") 
	orders_ai_count = get_agreement_order_count("make_with_ai") 
	orders_legal_count = get_agreement_order_count("make_with_legal_help") 
	orders_total_count = OrderAgreement.objects.filter(
		order_status="COMPLETED" # payment_status="PAYMENT_SUCCESS"
	).count()

	word_total_amount = get_agreement_order_total_amount("word")
	pdf_total_amount = get_agreement_order_total_amount("pdf")
	ai_total_amount = get_agreement_order_total_amount("make_with_ai") 
	legal_total_amount = get_agreement_order_total_amount("make_with_legal_help")

	word_pending_count = get_agreement_pending_order_count("word")
	pdf_pending_count = get_agreement_pending_order_count("pdf")
	ai_pending_count = get_agreement_pending_order_count("make_with_ai")
	legal_pending_count = get_agreement_pending_order_count("make_with_legal_help")
 
	# agreement_first_obj = Aggrement.objects.first()
	# agreement_word_price = agreement_first_obj.word_price - 1
	# agreement_pdf_price = agreement_first_obj.pdf_price - 1
	# agreement_ai_price = agreement_first_obj.make_with_ai_price - 1
	# agreement_legal_price= agreement_first_obj.make_with_legal_help_price - 1
	
	# word_pending_amount = word_pending_count * agreement_word_price
	# pdf_pending_amount = pdf_pending_count * agreement_pdf_price
	# ai_pending_amount = ai_pending_count * agreement_ai_price
	# legal_pending_amount = legal_pending_count * agreement_legal_price

	word_pending_amount = get_agreement_pending_order_amount('word')
	pdf_pending_amount = get_agreement_pending_order_amount('pdf')
	ai_pending_amount = get_agreement_pending_order_amount('make_with_ai')
	legal_pending_amount = get_agreement_pending_order_amount('make_with_legal_help')

	word_failed_count = get_agreement_failed_order_count("word")
	pdf_failed_count = get_agreement_failed_order_count("pdf")
	ai_failed_count = get_agreement_failed_order_count("make_with_ai")
	legal_failed_count = get_agreement_failed_order_count("make_with_legal_help")

	# word_failed_amount = word_failed_count * agreement_word_price
	# pdf_failed_amount = pdf_failed_count * agreement_pdf_price
	# ai_failed_amount = ai_failed_count * agreement_ai_price
	# legal_failed_amount = legal_failed_count * agreement_legal_price

	
	word_failed_amount = get_agreement_failed_order_amount('word')
	pdf_failed_amount = get_agreement_failed_order_amount('pdf')
	ai_failed_amount = get_agreement_failed_order_amount('make_with_ai')
	legal_failed_amount = get_agreement_failed_order_amount('make_with_legal_help')

	data = {}
	data['orders_word_count'] = orders_word_count
	data['orders_pdf_count'] = orders_pdf_count
	data["orders_ai_count"] = orders_ai_count
	data['orders_legal_count'] = orders_legal_count
	data['orders_total_count'] = orders_total_count

	data['word_total_amount'] = word_total_amount
	data['pdf_total_amount'] = pdf_total_amount
	data['ai_total_amount'] = ai_total_amount
	data['legal_total_amount'] = legal_total_amount
	data['total_amount'] = word_total_amount + pdf_total_amount + ai_total_amount + legal_total_amount

	data['word_pending_count'] = word_pending_count
	data['pdf_pending_count'] = pdf_pending_count
	data['ai_pending_count'] = ai_pending_count
	data['legal_pending_count'] = legal_pending_count
	data['total_pending_agreement_order_count'] =  word_pending_count + pdf_pending_count + ai_pending_count + legal_pending_count

	data['word_pending_amount'] = word_pending_amount
	data['pdf_pending_amount'] = pdf_pending_amount
	data['ai_pending_amount'] = ai_pending_amount
	data['legal_pending_amount'] = legal_pending_amount
	data['total_pending_agreement_order_amount'] =  word_pending_amount + pdf_pending_amount + ai_pending_amount + legal_pending_amount

	data['word_failed_amount'] = word_failed_amount
	data['pdf_failed_amount'] = pdf_failed_amount
	data['ai_failed_amount'] = ai_failed_amount
	data['legal_failed_amount'] = legal_failed_amount
	data['total_failed_agreement_order_amount'] =  word_failed_amount + pdf_failed_amount + ai_failed_amount + legal_failed_amount

	data['word_failed_count'] = word_failed_count
	data['pdf_failed_count'] = pdf_failed_count
	data['ai_failed_count'] = ai_failed_count
	data['legal_failed_count'] = legal_failed_count
	data['total_failed_agreement_order_count'] =  word_failed_count + pdf_failed_count + ai_failed_count + legal_failed_count

	return data



def get_agreement_orders_by_state():
	# Step 1: Annotate the StateMaster queryset with the count of related OrderAgreement objects
	queryset = StateMaster.objects.annotate(order_agreement_count=Count('userorder__user_orderAgreements'))
	
	# Step 2: Filter the queryset to include only states that have at least one related OrderAgreement
	queryset = queryset.filter(order_agreement_count__gt=0)
	
	if not queryset.exists():
		state_percentage_data = []
		return state_percentage_data  # Return an empty list if there are no OrderService objects
	
	
	# Step 3: Calculate the total count of OrderAgreement objects across all states
	total_count = sum(item.order_agreement_count for item in queryset)
	
	# Step 4: Calculate the percentage for each state as a float value and round to one decimal place
	percentages = []
	for item in queryset:
		percentage = round((item.order_agreement_count / total_count) * 100, 1)
		percentages.append((item, percentage))
	
	# Step 5: Adjust the percentages to ensure they sum up to 100%
	# Calculate the sum of the initial rounded percentages
	initial_sum = sum(percentage for item, percentage in percentages)
	difference = round(100 - initial_sum, 1)
	
	# Adjust the first state's percentage to account for the difference (to ensure the total is exactly 100)
	if percentages and difference != 0:
		item, percentage = percentages[0]
		percentages[0] = (item, round(percentage + difference, 1))
	
	# Step 6: Sort the queryset based on percentage in descending order
	percentages.sort(key=lambda x: x[1], reverse=True)
	
	# Step 7: Create a list to store the state name and percentage data, ensuring proper formatting
	state_percentage_data = [{'state_name': item.state_name, 'percentage': format(percentage, '.1f')} for item, percentage in percentages]
		
	# Calculate the total percentage (should be 100.0 after adjustments)
	total_value = sum(float(item['percentage']) for item in state_percentage_data)

	# Round to show 100
	rounded_percentage = round(total_value, 0)
	# # print(rounded_percentage)
	total = int(rounded_percentage)
	# # print(int_rounded_percentage)
	# Return the state percentage data
	return state_percentage_data



def get_total_agreement_count():
	count = Aggrement.objects.all().count()
	return count


def get_ordered_agreements_guest_users_count():
	unique_phone_count = (
		UserOrder.objects
		.filter(user__isnull=True)  # Ensure user is empty
		.exclude(phone_number__isnull=True)  # Exclude entries where phone_number is null
		.exclude(phone_number='')  # Exclude entries where phone_number is empty
		.values('phone_number')  # Group by phone_number
		.distinct()  # Ensure distinct phone_number entries
		.count()  # Get the count of unique phone_number entries
	)
	return unique_phone_count



def repeated_agreement_orders_by_user():
	"""
	Returns a dictionary with repeated orders by users.
	"""
	# Query to get counts by phone number
	all_queryset = OrderAgreement.objects.values('user__phone_number').annotate(
		count_total=Count('id'),
		count_word=Count(Case(When(agreement_service_option='word', then=1), output_field=IntegerField())),
		count_pdf=Count(Case(When(agreement_service_option='pdf', then=1), output_field=IntegerField())),
		count_ai=Count(Case(When(agreement_service_option='make_with_ai', then=1), output_field=IntegerField())),
		count_legal=Count(Case(When(agreement_service_option='make_with_legal_help', then=1), output_field=IntegerField()))
	).filter(user__phone_number__isnull=False).order_by('user__phone_number')

	# Prepare the response data
	all_response_data = []
	for item in all_queryset:
		user_instance = UserOrder.objects.filter(phone_number=item['user__phone_number']).last()
		user_str = str(user_instance)
		all_response_data.append({
			# 'phone_number': item['user__phone_number'],
			'user_str' : user_str,
			'count_total': item['count_total'],
			'count_word': item['count_word'],
			'count_pdf': item['count_pdf'],
			'count_ai': item['count_ai'],
			'count_legal': item['count_legal'],
		})
		
	# Query to get counts by phone number
	completed_queryset = all_queryset.filter(order_status="COMPLETED")

	# Prepare the response data
	completed_response_data = []
	for item in completed_queryset:
		user_instance = UserOrder.objects.filter(phone_number=item['user__phone_number']).last()
		user_str = str(user_instance)
		completed_response_data.append({
			# 'phone_number': item['user__phone_number'],
			'user_str' : user_str,
			'count_total': item['count_total'],
			'count_word': item['count_word'],
			'count_pdf': item['count_pdf'],
			'count_ai': item['count_ai'],
			'count_legal': item['count_legal'],
		})
	
	# Query to get counts by phone number
	pending_queryset = all_queryset.filter(order_status="PENDING")

	# Prepare the response data
	pending_response_data = []
	for item in pending_queryset:
		user_instance = UserOrder.objects.filter(phone_number=item['user__phone_number']).last()
		user_str = str(user_instance)
		pending_response_data.append({
			# 'phone_number': item['user__phone_number'],
			'user_str' : user_str,
			'count_total': item['count_total'],
			'count_word': item['count_word'],
			'count_pdf': item['count_pdf'],
			'count_ai': item['count_ai'],
			'count_legal': item['count_legal'],
		})
	

	# Query to get counts by phone number
	failed_queryset = all_queryset.filter(payment_status="PAYMENT_ERROR")

	# Prepare the response data
	failed_response_data = []
	for item in failed_queryset:
		user_instance = UserOrder.objects.filter(phone_number=item['user__phone_number']).last()
		user_str = str(user_instance)
		failed_response_data.append({
			# 'phone_number': item['user__phone_number'],
			'user_str' : user_str,
			'count_total': item['count_total'],
			'count_word': item['count_word'],
			'count_pdf': item['count_pdf'],
			'count_ai': item['count_ai'],
			'count_legal': item['count_legal'],
		})

	data = {}
	data['all_orders'] = all_response_data
	data['completed_orders'] = completed_response_data
	data['pending_orders'] = pending_response_data
	data['failed_orders'] = failed_response_data
	return data



def get_agreement_orders_for_sales_notifications(request):
	if request.method == 'POST':
		qs = None
		category_string = ""
		agreement_slug = request.POST.get('agreement_slug')
		agreement_obj = Aggrement.objects.filter(slug=agreement_slug).first()
		
		base_agreement_url = request.scheme + "://" + request.get_host() + reverse('aggrement:agreement_details', kwargs={'agreement_slug': agreement_obj.slug})

		if agreement_obj.aggrement_category.all():
			categories = [cat.slug for cat in agreement_obj.aggrement_category.all()]
			category_string = ','.join(categories)
		else:
			category_string = "general"
		url_category_str = "?category="+category_string
		complete_agreement_url = base_agreement_url + url_category_str

		all_orders = OrderAgreement.objects.filter(payment_status="PAYMENT_SUCCESS").order_by('?').prefetch_related('agreement__aggrement_category')
  
		if all_orders.exists():
			agreement_orders = all_orders.filter(agreement__slug=agreement_slug)
			if agreement_orders:
			
				agreement_orders_qs = agreement_orders.annotate(
					agreement_url=Value(complete_agreement_url, output_field=CharField())
				).annotate(
					order_type=Value('agreement', output_field=CharField())
				).values(
					'id', 'user__first_name', 'agreement__title', 'user__state__state_name', 'order_type', 'agreement_url'
				)
				qs = agreement_orders_qs
			else:

				a_orders = all_orders[:10]
				a_orders_qs = a_orders.annotate(
					agreement__title=Value(agreement_obj.title, output_field=CharField())
				).annotate(
					agreement_url=Value(complete_agreement_url, output_field=CharField())
				).annotate(
					order_type=Value('agreement', output_field=CharField())
				).values(
					'id', 'user__first_name', 'agreement__title', 'user__state__state_name', 'order_type', 'agreement_url'
				)
				qs = a_orders_qs
		if qs:
			return JsonResponse({'orders_exist': True, 'orders': list(qs)})
		else:
			return JsonResponse({'orders_exist': False})





def faq_query_view(request, agreement_slug):
	data = dict()
	try:
		if request.method == "POST":
			query_form = AgreementFAQForm(request.POST or None)
			
			if query_form.is_valid():
				agreement = Aggrement.objects.filter(slug=agreement_slug).first()
	
				base_agreement_url = request.scheme + "://" + request.get_host() + reverse('aggrement:agreement_details', kwargs={'agreement_slug': agreement.slug})

				if agreement.aggrement_category.all():
					categories = [cat.slug for cat in agreement.aggrement_category.all()]
					category_string = ','.join(categories)
				else:
					category_string = "general"
				url_category_str = "?category="+category_string
				complete_agreement_url = base_agreement_url + url_category_str
				
				# Begin reCAPTCHA validation
				recaptcha_response = request.POST.get('g-recaptcha-response')
				url = 'https://www.google.com/recaptcha/api/siteverify'
				values = {
					'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
					'response': recaptcha_response
				}
				data = urllib.parse.urlencode(values).encode()
				req =  urllib.request.Request(url, data=data)
				response = urllib.request.urlopen(req)
				result = json.loads(response.read().decode())
				# End reCAPTCHA validation

				if result['success']:
					query = query_form.save(commit=False)
					query.agreement = agreement
					query.save()
					page_url = complete_agreement_url
					source_url = page_url
					faq_page_url = page_url
					thread = threading.Thread(target=send_email_for_agreement_faq_inquiry, args=(query, query.email, source_url, faq_page_url))
					thread.start()
					return JsonResponse({'success': True, 'message': 'Your question received successfully, our team will get back to you soon!'})
				else:
					return JsonResponse({'success': False, 'message': 'Invalid reCAPTCHA. Please try again.'})

			else:
				return JsonResponse({'success': False, 'errors': query_form.errors})

		else:
			query_form = AgreementFAQForm()

		context = {'form': query_form}
		data['html_form'] = render_to_string('faq_form.html', context, request=request)
	except Exception as e:
		data['error'] = str(e)
		
	return JsonResponse(data)


# ======================================== Extra =========================================================


# def payment_done2(request, order_id):
# 	order_obj = OrderAgreement.objects.filter(id=1).first()
# 	context = {
# 		'order': order_obj
# 	}
# 	return render(request, 'payment_done_copy.html', context)



# from .models import StampDutyChargesWithImages

# def add_fixed(request):  # in database
#     all_agg = Aggrement.objects.all()
#     for a_obj in all_agg:
#         s_obj = StampDutyChargesWithImages.objects.filter(amount=10).first()
#         AgreementWithFixedStampDutyInAllStates.objects.get_or_create(agreement=a_obj, stamp_duty=s_obj)
#         print("================= added for agg_obj id : ",a_obj.id)
#     return HttpResponse("done")
	

# def add_different(request):  # in database
#     all_agg = Aggrement.objects.all()
#     for a_obj in all_agg:
#         s_obj = StampDutyChargesWithImages.objects.filter(amount=10).first()
#         state_obj = StateMaster.objects.filter(state_name="Delhi").first()
#         AgreementWithDifferentStampDutyInAllStates.objects.get_or_create(agreement=a_obj, stamp_duty=s_obj, state=state_obj)
#         print("================= added for agg_obj id : ",a_obj.id)
#     return HttpResponse("done")


# def agreement_set_to_fixed(request):
#     all_agg = Aggrement.objects.all()
#     for a_obj in all_agg:
#         a_obj.is_stamp_duty_needed = True
#         a_obj.is_stamp_duty_fixed_for_all_states = True
#         a_obj.save()
#     return HttpResponse("done")


# def remove_objects(request):
#     AgreementWithDifferentStampDutyInAllStates.objects.all().delete()
#     return HttpResponse("done")


# def add_objects(request):
#     agg_obj = Aggrement.objects.get(id=818)
#     # df = AgreementWithDifferentStampDutyInAllStates.objects.get(id=2863)
#     state_obj = AgreementStampDutyState.objects.get(id=2)
#     stamp_obj = StampDutyChargesWithImages.objects.get(id=2)
#     df = AgreementWithDifferentStampDutyInAllStates(agreement=agg_obj, state=state_obj, stamp_duty=stamp_obj)
#     df.save()
#     return HttpResponse("done")


# def my_view(request, state_name=None):
#     if state_name is not None:
#         return HttpResponse(f"state name provided: {state_name}")
#     else:
#         return HttpResponse("state name not provided")
	


